# -*- coding: utf-8 -*-
from enum import Enum
from typing import TYPE_CHECKING

from tortoise import fields
from tortoise.functions import Sum
from tortoise.manager import Manager
from tortoise.transactions import in_transaction

from app.models.base import BasicFieldsModel


class InboundOrderStatusEnum(int, Enum):
    PENDING_SCAN = 1
    PENDING_SHIP = 2
    SHIPPING = 3
    COMPLETED = 4

    @property
    def label(self) -> str:
        """获取枚举的中文描述"""
        labels = {self.PENDING_SCAN: "待扫描", self.PENDING_SHIP: "待发货", self.SHIPPING: "发货中", self.COMPLETED: "已完成"}
        return labels.get(self, "未知状态")

    @classmethod
    def choices(cls) -> list[dict]:
        """获取所有选项的列表，用于前端下拉框等"""
        return [
            {"value": cls.PENDING_SCAN.value, "label": "待扫描"},
            {"value": cls.PENDING_SHIP.value, "label": "待发货"},
            {"value": cls.SHIPPING.value, "label": "发货中"},
            {"value": cls.COMPLETED.value, "label": "已完成"},
        ]


class ShippingTypeEnum(int, Enum):
    SINGLE = 1
    DOUBLE = 2
    TRIPLE = 3
    MULTIPLE = 4

    @property
    def label(self) -> str:
        """获取枚举的中文描述"""
        labels = {self.SINGLE: "一单一件", self.DOUBLE: "一单两件", self.TRIPLE: "一单三件", self.MULTIPLE: "一单多件"}
        return labels.get(self, "未知类型")

    @classmethod
    def choices(cls) -> list[dict]:
        """获取所有选项的列表，用于前端下拉框等"""
        return [
            {"value": cls.SINGLE.value, "label": "一单一件"},
            {"value": cls.DOUBLE.value, "label": "一单两件"},
            {"value": cls.TRIPLE.value, "label": "一单三件"},
            {"value": cls.MULTIPLE.value, "label": "一单多件"},
        ]


class CertTypeEnum(int, Enum):
    SINGLE = 1
    RANDOM = 2
    NO_CERT = 3

    @property
    def label(self) -> str:
        """获取枚举的中文描述"""
        labels = {self.SINGLE: "一物一证", self.RANDOM: "随机发证", self.NO_CERT: "不发证书"}
        return labels.get(self, "未知方式")

    @classmethod
    def choices(cls) -> list[dict]:
        """获取所有选项的列表，用于前端下拉框等"""
        return [{"value": cls.SINGLE.value, "label": "一物一证"}, {"value": cls.RANDOM.value, "label": "随机发证"}, {"value": cls.NO_CERT.value, "label": "不发证书"}]


class WayBillCodeBindCertStatusEnum(int, Enum):
    UNBIND = 1
    BIND = 2

    @property
    def label(self) -> str:
        """获取枚举的中文描述"""
        labels = {self.UNBIND: "未绑定", self.BIND: "已绑定"}
        return labels.get(self, "未知状态")

    @classmethod
    def choices(cls) -> list[dict]:
        """获取所有选项的列表，用于前端下拉框等"""
        return [{"value": cls.UNBIND.value, "label": "未绑定"}, {"value": cls.BIND.value, "label": "已绑定"}]


class WaybillStatusEnum(int, Enum):
    PENDING = 1
    SHIPPED = 2
    COMPLETED = 3

    @property
    def label(self) -> str:
        """获取枚举的中文描述"""
        labels = {self.PENDING: "待发货", self.SHIPPED: "已发货", self.COMPLETED: "已完成"}
        return labels.get(self, "未知状态")

    @classmethod
    def choices(cls) -> list[dict]:
        """获取所有选项的列表，用于前端下拉框等"""
        return [{"value": cls.PENDING.value, "label": "待发货"}, {"value": cls.SHIPPED.value, "label": "已发货"}, {"value": cls.COMPLETED.value, "label": "已完成"}]


class CertificateTypeEnum(int, Enum):
    ONE_TO_ONE = 1
    RANDOM = 2

    @property
    def label(self) -> str:
        """获取枚举的中文描述"""
        labels = {self.ONE_TO_ONE: "一物一证", self.RANDOM: "随机发证"}
        return labels.get(self, "未知类型")

    @classmethod
    def choices(cls) -> list[dict]:
        """获取所有选项的列表，用于前端下拉框等"""
        return [{"value": cls.ONE_TO_ONE.value, "label": "一物一证"}, {"value": cls.RANDOM.value, "label": "随机发证"}]


class ItemTypeEnum(int, Enum):
    PRODUCT_ONLY = 1
    PRODUCT_WITH_CERT = 2
    CERT_ONLY = 3

    @property
    def label(self) -> str:
        """获取枚举的中文描述"""
        labels = {self.PRODUCT_ONLY: "仅商品", self.PRODUCT_WITH_CERT: "商品+证书", self.CERT_ONLY: "随机发证"}
        return labels.get(self, "未知类型")

    @classmethod
    def choices(cls) -> list[dict]:
        """获取所有选项的列表，用于前端下拉框等"""
        return [
            {"value": cls.PRODUCT_ONLY.value, "label": "仅商品"},
            {"value": cls.PRODUCT_WITH_CERT.value, "label": "商品+证书"},
            {"value": cls.CERT_ONLY.value, "label": "随机发证"},
        ]


class InboundOrder(BasicFieldsModel):
    code = fields.CharField(max_length=32, description="入库单号", db_index=True)
    check_company_name = fields.CharField(max_length=64, description="公司名称")
    check_user = fields.ForeignKeyField("models.User", description="清点人员", related_name="inbound_orders")
    product_count = fields.IntField(description="商品数量", default=1)
    cert_count = fields.IntField(description="证书数量", default=1)
    waybill_count = fields.IntField(description="面单数量", default=1)
    status = fields.IntField(description="状态(1:待扫描, 2:待发货, 3:发货中, 4:已完成)", default=InboundOrderStatusEnum.PENDING_SCAN.value)
    company_id: int
    company = fields.ForeignKeyField(
        "models.Company",
        description="公司",
        related_name="inbound_orders",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )

    if TYPE_CHECKING:
        details_set = Manager(model="InboundOrderDetail")

    class Meta:
        table = "cert_inbound_order"
        table_description = "入库单"
        unique_together = ("code", "company")

    async def save(self, *args, **kwargs):
        async with in_transaction("default"):
            if not self.code:
                self.code = await self.generate_sequence_code(self.company_id, prefix="RK", length=3)

            return await super().save(*args, **kwargs)

    @property
    async def current_status(self):
        """
        当前状态，根据inbound_order_detail进行判断
        待扫描：没有任务
        待发货：有任务单，但是没有发货人
        发货中：任意任务单被领取（有发货人）
        已完成：全部任务单都被领取（都有发货人）
        """
        details = await self.details_set.filter(is_deleted=False).all()
        # 总的明细单量
        total_batch_count = sum([detail.batch_count for detail in details])

        if not details:
            return InboundOrderStatusEnum.PENDING_SCAN.value

        if all([detail.shipping_user_id is not None for detail in details]) and total_batch_count == self.waybill_count:
            return InboundOrderStatusEnum.COMPLETED.value

        if any([detail.shipping_user_id is not None for detail in details]):
            return InboundOrderStatusEnum.SHIPPING.value

        return InboundOrderStatusEnum.PENDING_SHIP.value

    @property
    async def current_remaining_waybill_count(self) -> int:
        """面单余量

        Returns:
            int: 面单余量
        """

        details = await self.details_set.filter(is_deleted=False).annotate(total_batch_count=Sum("batch_count")).values("total_batch_count")
        total = details[0].get("total_batch_count") or 0 if details else 0
        return self.waybill_count - total


class InboundOrderDetail(BasicFieldsModel):
    inbound_order = fields.ForeignKeyField(
        "models.InboundOrder",
        description="入库单",
        on_delete=fields.CASCADE,
        db_constraint=False,
        related_name="details_set",
    )
    code = fields.CharField(max_length=32, description="任务单号", db_index=True)
    # 发货类型 1一单一件 2一单两件 3一单三件 4一单多件
    shipping_type = fields.IntField(description="发货类型(1:一单一件, 2:一单两件, 3:一单三件, 4:一单多件)", default=ShippingTypeEnum.SINGLE.value)
    # 发证方式 1一物一证 2随机发证 3不发证书
    cert_type = fields.IntField(description="发证方式(1:一物一证, 2:随机发证, 3:不发证书)", default=CertTypeEnum.SINGLE.value)
    # 发货人
    shipping_user_id: int | None
    shipping_user = fields.ForeignKeyField(
        "models.User",
        description="发货人",
        on_delete=fields.CASCADE,
        db_constraint=False,
        null=True,
    )
    # 每批数量/面单数量
    batch_count = fields.IntField(description="每批数量", default=1)
    # 商品数量
    product_count = fields.IntField(description="商品数量", default=1)
    # 证书数量
    cert_count = fields.IntField(description="证书数量", default=1)
    company_id: int
    company = fields.ForeignKeyField(
        "models.Company",
        description="公司",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )

    if TYPE_CHECKING:
        waybills_set = Manager(model="InboundOrderWaybill")

    class Meta:
        table = "cert_inbound_order_detail"
        table_description = "入库单明细"
        unique_together = ("code", "company")

    async def save(self, *args, **kwargs):
        async with in_transaction("default"):
            if not self.code:
                self.code = await self.generate_sequence_code(self.company_id, prefix="QD", length=5)

            return await super().save(*args, **kwargs)


class ShippingWaybill(BasicFieldsModel):
    """
    面单表 - 一个面单一条记录
    面单信息来自 inbound_order_detail，商品信息来自 ShippingSubscribe
    """

    # === 基础标识信息 ===
    waybill_code = fields.CharField(max_length=32, description="快递单号/面单编号", db_index=True, unique=True)

    # === 关联信息 ===
    inbound_order = fields.ForeignKeyField(
        "models.InboundOrder",
        description="入库单",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )
    inbound_order_detail = fields.ForeignKeyField(
        "models.InboundOrderDetail",
        description="入库单明细",
        on_delete=fields.CASCADE,
        db_constraint=False,
        related_name="waybills_set",
    )
    company = fields.ForeignKeyField(
        "models.Company",
        description="公司",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )
    shipping_user = fields.ForeignKeyField(
        "models.User",
        description="发货人",
        on_delete=fields.CASCADE,
        db_constraint=False,
        null=True,
    )

    # === 订单信息（来自 ShippingSubscribe 订阅表）===
    order_id = fields.CharField(max_length=128, description="线上订单号", db_index=True, null=True)
    logistics_company = fields.CharField(max_length=100, description="物流公司", null=True)
    send_date = fields.DatetimeField(description="发货时间", null=True)
    total_qty = fields.IntField(description="总数量", default=0)

    # === 冗余字段用于快速查询 ===
    inbound_order_code = fields.CharField(max_length=32, description="入库单号(冗余)", db_index=True, null=True)
    inbound_order_detail_code = fields.CharField(max_length=32, description="入库单明细号(冗余)", db_index=True, null=True)
    shipping_user_name = fields.CharField(max_length=64, description="发货人姓名(冗余)", null=True)

    class Meta:
        table = "cert_shipping_waybill"
        table_description = "面单表"
        indexes = [
            # 快递单号查询 - 最常用的查询场景
            ("waybill_code", "company_id"),
            # 订单号查询 - 客服查询订单状态
            ("order_id", "company_id"),
            # 发货人查询 - 查看发货人的工作量
            ("shipping_user_id", "company_id"),
            # 入库单查询 - 根据入库单号查找所有相关面单
            ("inbound_order_code", "company_id"),
            # 入库单明细查询 - 根据明细单号查找面单
            ("inbound_order_detail_code", "company_id"),
        ]


class WaybillProduct(BasicFieldsModel):
    """
    面单商品表 - 合并商品信息和证书绑定
    商品信息来自 ShippingSubscribe 订阅表，证书信息通过绑定操作添加

    数据类型：
    1. 商品记录（item_type=1）：只有商品信息，证书字段为空
    2. 商品+证书记录（item_type=2）：一物一证，商品和证书信息都有
    3. 随机发证记录（item_type=3）：只有证书信息，商品字段为空或冗余
    """

    # === 关联信息 ===
    waybill = fields.ForeignKeyField(
        "models.ShippingWaybill",
        description="面单",
        on_delete=fields.CASCADE,
        db_constraint=False,
        related_name="products_set",
    )
    company = fields.ForeignKeyField(
        "models.Company",
        description="公司",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )
    inbound_order = fields.ForeignKeyField(
        "models.InboundOrder",
        description="入库单",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )
    inbound_order_detail = fields.ForeignKeyField(
        "models.InboundOrderDetail",
        description="入库单明细",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )

    # === 记录类型 ===
    item_type = fields.IntField(description="记录类型(1:仅商品, 2:商品+证书, 3:随机发证)", default=1, db_index=True)

    # === 商品信息（来自 ShippingSubscribe 订阅表）===
    sku_id = fields.CharField(max_length=128, description="商品SKU_ID", db_index=True, null=True)
    product_name = fields.CharField(max_length=256, description="商品名称", null=True)
    product_image = fields.CharField(max_length=500, description="商品图片", null=True)
    quantity = fields.IntField(description="商品数量", default=0)
    outer_oi_id = fields.CharField(max_length=128, description="外部订单项ID", db_index=True, null=True)
    oi_id = fields.CharField(max_length=128, description="订单项ID", db_index=True, null=True)

    # === 证书信息（证书绑定时填充）===
    cert_sn_code = fields.CharField(max_length=128, description="证书SN码", db_index=True, null=True, unique=True)
    cert_type = fields.IntField(description="证书类型(1:一物一证, 2:随机发证)", null=True, db_index=True)
    product_sn_code = fields.CharField(max_length=128, description="商品SN码", db_index=True, null=True)

    # === 绑定信息 ===
    bind_status = fields.IntField(
        description="绑定状态(1:未绑定, 2:已绑定)",
        default=WayBillCodeBindCertStatusEnum.UNBIND.value,
        db_index=True,
    )
    bind_time = fields.DatetimeField(description="绑定时间", null=True)

    # === 冗余字段用于快速查询 ===
    waybill_code = fields.CharField(max_length=32, description="快递单号(冗余)", db_index=True, null=True)
    order_id = fields.CharField(max_length=128, description="线上订单号(冗余)", db_index=True, null=True)
    # author_id = fields.CharField(max_length=128, description="达人ID(冗余)", db_index=True, null=True)
    # shop_id = fields.CharField(max_length=128, description="店铺ID(冗余)", db_index=True, null=True)
    # order_time = fields.DatetimeField(description="订单时间(冗余)", null=True, db_index=True)
    # pay_time = fields.DatetimeField(description="支付时间(冗余)", null=True, db_index=True)

    class Meta:
        table = "cert_waybill_product"
        table_description = "面单商品表（合并证书绑定）"
        indexes = [
            # 记录类型查询
            ("item_type", "company_id"),
            # SKU查询 - 根据商品SKU查找
            ("sku_id", "company_id"),
            # 证书查询 - 根据证书号查找相关信息
            ("cert_sn_code", "company_id"),
            # 商品SN码查询
            ("product_sn_code", "company_id"),
            # 外部订单项ID查询
            ("outer_oi_id", "company_id"),
            # 快递单号查询(冗余)
            ("waybill_code", "company_id"),
            # 订单号查询(冗余)
            ("order_id", "company_id"),
            # 证书类型查询
            ("cert_type", "company_id"),
            # 复合查询 - 面单+记录类型
            ("waybill_id", "item_type"),
        ]
