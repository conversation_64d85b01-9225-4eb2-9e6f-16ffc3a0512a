# -*- coding: utf-8 -*-
from typing import Optional

from tortoise.expressions import Q
from tortoise.queryset import QuerySet

from app.filters.base import BaseFilterSet, FilterField, PageSizePaginatorParams
from app.models import WaybillProduct


class PendingShippingFilterParams(BaseFilterSet, PageSizePaginatorParams):
    bind_status: Optional[str | None] = FilterField(None, description="状态(1:未绑定, 2:已绑定", lookup_expr="exact")
    q: Optional[str | None] = FilterField(None, description="搜索关键字(快递单号/订单号/商品SN码/证书号)", method="filter_q")

    class Meta:
        model = WaybillProduct

    async def filter_q(self, queryset: QuerySet, field_name: str, value: str) -> QuerySet:
        """自定义过滤方法：根据关键字过滤"""
        return queryset.filter(
            Q(waybill_code__icontains=value)
            | Q(order_id__icontains=value)
            | Q(product_sn_code__icontains=value)
            | Q(
                cert_sn_code__icontains=value,
            )
        )
