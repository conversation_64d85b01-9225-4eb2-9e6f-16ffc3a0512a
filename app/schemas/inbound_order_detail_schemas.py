# -*- coding: utf-8 -*-


from typing import Optional

from pydantic import BaseModel, Field, field_validator
from tortoise.contrib.pydantic import PydanticModel, pydantic_model_creator

from app.core.exceptions import BusinessException
from app.models.inbound_order_models import CertType<PERSON>num, InboundOrderDetail, ShippingTypeEnum

InboundOrderDetailInfoPydantic: PydanticModel = pydantic_model_creator(
    InboundOrderDetail,
    name="InboundOrderDetail",
    exclude=(
        "create_user_id",
        "update_user_id",
        "is_deleted",
        "company_id",
        "company",
    ),
)


class InboundOrderDetailCreateRequest(BaseModel):
    shipping_type: int = Field(ShippingTypeEnum.SINGLE.value, description="发货类型(1:单件, 2:双件, 3:三件, 4:多件)")
    cert_type: int = Field(CertTypeEnum.SINGLE.value, description="发证方式(1:一物一证, 2:随机, 3:无证)")
    shipping_user_id: Optional[int | str | None] = Field(..., description="发货人,用户user_id")
    batch_count: int = Field(1, description="每批数量", ge=1)
    product_count: int = Field(1, description="商品数量", ge=1)
    cert_count: int = Field(1, description="证书数量", ge=1)
    waybill_codes: list[str] = Field(default_factory=list, description="面单编号")

    @field_validator("shipping_user_id")
    def validate_shipping_user_id(cls, value):
        if not value:
            raise BusinessException(msg="发货人不能为空")

        if not str(value).isdigit():
            raise BusinessException(msg="发货人信息错误")

        return value

    @field_validator("shipping_type")
    def validate_shipping_type(cls, value):
        if value not in [ShippingTypeEnum.SINGLE.value, ShippingTypeEnum.DOUBLE.value, ShippingTypeEnum.TRIPLE.value, ShippingTypeEnum.MULTIPLE.value]:
            raise BusinessException(msg="发货类型错误")
        return value

    @field_validator("cert_type")
    def validate_cert_type(cls, value):
        if value not in [CertTypeEnum.SINGLE.value, CertTypeEnum.RANDOM.value, CertTypeEnum.NO_CERT.value]:
            raise BusinessException(msg="发证方式错误")
        return value

    @field_validator("waybill_codes")
    def validate_waybill_codes(cls, value):
        if not value:
            raise BusinessException(msg="面单编号不能为空")

        return list(set(value))


class InboundOrderDetailInfoResponse(BaseModel):
    code: str = Field(..., description="明细编号")
    shipping_type: int = Field(..., description="发货类型(1:单件, 2:双件, 3:三件, 4:多件")
    cert_type: int = Field(..., description="发证方式(1:一物一证, 2:随机, 3:无证")
    batch_count: int = Field(..., description="每批数量", ge=1)
    product_count: int = Field(..., description="商品数量", ge=1)
    cert_count: int = Field(..., description="证书数量", ge=1)
    shipping_user_id: int | str | None = Field(..., description="发货人，用户user_id")
    shipping_user_name: str = Field(..., description="发货人姓名")
    waybill_codes: list[str] = Field(..., description="面单编号列表")


class InboundOrderDetailBatchUpdateRequest(BaseModel):
    shipping_user_id: int | str | None = Field(..., description="发货人，用户user_id")

    @field_validator("shipping_user_id")
    def validate_shipping_user_id(cls, value):
        if not value:
            raise BusinessException(msg="发货人不能为空")

        if not str(value).isdigit():
            raise BusinessException(msg="发货人信息错误")

        return value
