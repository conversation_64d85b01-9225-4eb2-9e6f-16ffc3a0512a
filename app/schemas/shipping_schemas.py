# -*- coding: utf-8 -*-


from pydantic import BaseModel, Field


class ShippingPendingOrderListResponse(BaseModel):
    id: int = Field(..., description="主键ID")
    sku_id: int | str = Field(..., description="商品SKU_ID")
    product_name: str | None = Field(..., description="商品名称")
    product_image: str | None = Field(..., description="商品图片")
    waybill_code: str | None = Field(..., description="快递单号")
    order_id: str = Field(..., description="线上订单号")
    bind_status: int = Field(..., description="绑定状态(1:未绑定, 2:已绑定)")
    shipping_type: int = Field(..., description="发货类型(1:一单一件, 2:一单两件, 3:一单三件, 4:一单多件)")
    cert_type: int = Field(..., description="证书类型(1:一物一证, 2:随机发证)")
    cert_sn_code: str | None = Field(..., description="证书SN码")
    product_sn_code: str | None = Field(..., description="商品SN码")
    quantity: int = Field(..., description="商品数量")
    outer_oi_id: str | None = Field(..., description="外部订单项ID")
    oi_id: str | None = Field(..., description="订单项ID")


class WayBillProductBindCertRequest(BaseModel):
    id: int = Field(..., description="待发货订单列表的主键id")
    cert_sn_code: str = Field(..., description="证书SN码")
    product_sn_code: str | None = Field(None, description="商品SN码")
