from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, field_validator
from tortoise.contrib.pydantic import PydanticModel, pydantic_model_creator

from app.core.exceptions import BusinessException
from app.models.inbound_order_models import InboundOrder

InboundOrderInfoPydantic: PydanticModel = pydantic_model_creator(
    InboundOrder,
    name="InboundOrder",
    exclude=(
        "id",
        "create_user_id",
        "update_user_id",
        "is_deleted",
        "company_id",
        "company",
    ),
)


class InboundOrderListResponse(BaseModel):
    code: str = Field(..., description="入库单号")
    check_company_name: str = Field(..., description="公司名称")
    check_user_id: str | int = Field(..., description="清点人员user_id")
    check_user_name: str = Field(..., description="清点人员username")
    create_time: datetime = Field(..., description="时间")
    each_count: int = Field(..., description="分批数量", ge=0)
    per_batch_count: int = Field(..., description="单批数量", ge=0)
    product_count: int = Field(..., description="商品数量", ge=0)
    cert_count: int = Field(..., description="证书数量", ge=0)
    waybill_count: int = Field(..., description="面单数量", ge=0)
    has_check_product_count: int = Field(..., description="已清点商品数量", ge=0)
    has_check_cert_count: int = Field(..., description="已清点证书数量", ge=0)
    has_check_waybill_count: int = Field(..., description="已清点面单数量", ge=0)
    remaining_waybill_count: int = Field(..., description="面单余量", ge=0)
    status: int = Field(..., description="状态")


class InboundOrderCreateRequest(BaseModel):
    check_company_name: str = Field(..., description="公司名称", max_length=64)
    check_user_id: int | str = Field(..., description="清点人员user_id")
    product_count: int = Field(..., description="商品数量", ge=1)
    cert_count: int = Field(..., description="证书数量", ge=1)
    waybill_count: int = Field(..., description="面单数量", ge=1)

    @field_validator("check_user_id")
    def validate_check_user_id(cls, v):
        if not v:
            raise BusinessException(msg="清点人员不能为空")

        if not str(v).isdigit():
            raise BusinessException(msg="清点人员信息错误")

        return v


class InboundOrderUpdateRequest(BaseModel):
    check_company_name: Optional[str] = Field(..., description="公司名称", max_length=64)
    check_user_id: Optional[int | str] = Field(..., description="清点人员user_id")
    product_count: Optional[int] = Field(..., description="商品数量", ge=1)
    cert_count: Optional[int] = Field(..., description="证书数量", ge=1)
    waybill_count: Optional[int] = Field(..., description="面单数量", ge=1)


class BatchDeleteInboundOrderRequest(BaseModel):
    code_list: list[str] = Field(..., description="入库单号code列表", min_length=1)
