# 导入所有路由模块
from app.api.endpoints.company_endpoints import company_router
from app.api.endpoints.inbound_order_detail_endpoints import inbound_order_detail_router
from app.api.endpoints.inbound_order_endpoints import inbound_order_router
from app.api.endpoints.login_endpoints import login_router
from app.api.endpoints.orders_endpoints import orders_router

# shipping_router has been removed
from app.api.endpoints.subscribe_endpoints import subscribe_router
from app.api.endpoints.users_endpoints import user_router

__all__ = [
    "user_router",
    "company_router",
    "inbound_order_router",
    "login_router",
    "subscribe_router",
    "orders_router",
    "inbound_order_detail_router",
    "shipping_router",
]
