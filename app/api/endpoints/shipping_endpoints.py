# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import APIRouter, Depends, Query
from fastapi_pagination import Page

from app.api.dependencies import get_current_user
from app.filters.shipping_filters import PendingShippingFilterParams
from app.models import User
from app.schemas.base import RestfulResponse
from app.schemas.shipping_schemas import ShippingPendingOrderListResponse
from app.services.shipping_services import ShippingOrderService

shipping_router = APIRouter()


@shipping_router.get(
    "pending-shipping",
    summary="获取待发货面单列表",
    response_model=RestfulResponse[Page[ShippingPendingOrderListResponse]],
)
async def get_pending_shipping(filter_query: Annotated[PendingShippingFilterParams, Query()], current_user: User = Depends(get_current_user)):
    service = ShippingOrderService()
    data = await service.get_pending_shipping_orders(current_user, filter_query)
    return RestfulResponse(data=data)
