# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import APIRouter, Depends, Query
from fastapi_pagination import Page

from app.api.dependencies import get_current_user
from app.filters.shipping_filters import PendingShippingFilterParams
from app.models import User
from app.schemas.base import RestfulResponse
from app.schemas.shipping_schemas import ShippingPendingOrderListResponse, WayBillProductBindCertRequest
from app.services.shipping_services import ShippingOrderService

shipping_router = APIRouter()


@shipping_router.get(
    "/pending-shipping",
    summary="获取待发货面单列表",
    response_model=RestfulResponse[Page[ShippingPendingOrderListResponse]],
)
async def get_pending_shipping(filter_query: Annotated[PendingShippingFilterParams, Query()], current_user: User = Depends(get_current_user)):
    service = ShippingOrderService()
    data = await service.get_pending_shipping_orders(current_user, filter_query)
    return RestfulResponse(data=data)


@shipping_router.post("/bind-cert", summary="绑定证书", response_model=RestfulResponse[None])
async def bind_cert(bind_data: WayBillProductBindCertRequest, current_user: User = Depends(get_current_user)):
    service = ShippingOrderService()
    await service.bind_cert(bind_data, current_user)
    return RestfulResponse()
