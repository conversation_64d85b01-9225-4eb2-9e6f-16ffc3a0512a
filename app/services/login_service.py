import json
import random
import string
from datetime import datetime, timed<PERSON><PERSON>
from typing import <PERSON><PERSON>

from alibabacloud_dysmsapi20170525 import models as dysmsapi_models
from alibabacloud_dysmsapi20170525.client import Client as DysmsapiClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from fastapi import BackgroundTasks
from jose import jwt
from loguru import logger

from app.core.config import settings
from app.core.exceptions import InvalidTokenException
from app.core.redis import RedisClient
from app.models import User


class LoginServices:
    @classmethod
    def get_aliyun_client(cls) -> DysmsapiClient:
        """
        获取阿里云短信客户端
        """
        config = open_api_models.Config(
            access_key_id=settings.ALIYUN_ACCESS_KEY_ID,
            access_key_secret=settings.ALIYUN_ACCESS_KEY_SECRET,
        )
        config.endpoint = "dysmsapi.aliyuncs.com"
        return DysmsapiClient(config)

    @classmethod
    def generate_sms_code(cls) -> str:
        """
        生成随机验证码
        """
        return "".join(random.choices(string.digits, k=settings.SMS_CODE_LENGTH))

    @classmethod
    async def send_sms_code(
        cls,
        mobile: str,
        background_tasks: BackgroundTasks,
        origin_code: str = "86",
    ) -> Tuple[bool, str]:
        """
        发送短信验证码
        :param origin_code: 国家代码
        :param mobile: 手机号
        :param background_tasks: 后台任务
        :return: (是否成功, 错误信息)
        """
        # 生成验证码
        code = cls.generate_sms_code()

        # 将发送短信任务添加到后台任务
        background_tasks.add_task(cls._send_sms, mobile, code, origin_code)

        try:
            # 使用全局 Redis 客户端存储验证码
            redis = RedisClient.get_redis_client()
            key = f"sms_code:{mobile}"
            await redis.set(key, code, ex=int(timedelta(minutes=settings.SMS_CODE_EXPIRE_MINUTES).total_seconds()))
            return True, ""
        except Exception as e:
            return False, f"Redis错误: {str(e)}"

    @classmethod
    def _send_sms(cls, mobile: str, code: str, origin_code: str = "86"):
        """
        实际发送短信的方法（在后台任务中执行）
        """
        try:
            client = cls.get_aliyun_client()
            send_request = dysmsapi_models.SendSmsRequest(
                phone_numbers=f"{origin_code}{mobile}",
                sign_name=settings.ALIYUN_SMS_SIGN_NAME,
                template_code=settings.ALIYUN_SMS_TEMPLATE_CODE,
                template_param=json.dumps({"code": code}),
            )
            runtime = util_models.RuntimeOptions()
            ret = client.send_sms_with_options(send_request, runtime)
            logger.info(f">>> 发送短信成功: {ret}")
        except Exception as e:
            # 这里可以添加日志记录
            logger.warning(f"发送短信失败: {str(e)}")

    @classmethod
    async def verify_sms_code(cls, mobile: str, code: str) -> bool:
        """
        验证短信验证码
        :param mobile: 手机号
        :param code: 验证码
        :return: 是否验证成功
        """
        try:
            redis = RedisClient.get_redis_client()
            key = f"sms_code:{mobile}"
            stored_code = await redis.get(key)

            if not stored_code:
                return False

            # 验证成功后删除验证码
            if stored_code == code:
                await redis.delete(key)
                return True

            return False
        except Exception as e:
            logger.warning(f"校验sms错误: {str(e)}")
            return False

    @classmethod
    def create_access_token(cls, data: dict) -> str:
        """
        创建访问令牌
        """
        to_encode = data.copy()
        expire = datetime.now() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})

        encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
        return encoded_jwt

    @classmethod
    def create_refresh_token(cls, data: dict) -> str:
        """
        创建刷新令牌
        """
        to_encode = data.copy()
        expire = datetime.now() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "refresh": True})
        encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
        return encoded_jwt

    @classmethod
    async def login(cls, user: User) -> dict:
        """
        用户登录
        :param user: 用户对象
        :return: 令牌信息
        """
        if user.status == 2:
            raise InvalidTokenException(msg="用户已被禁用")

        data = {"sub": user.username}
        access_token = cls.create_access_token(data)
        refresh_token = cls.create_refresh_token(data)

        # 记录用户最后登录时间
        user.last_login_time = datetime.now()
        await user.save(update_fields=["last_login_time"])

        return {"access_token": access_token, "refresh_token": refresh_token, "token_type": "bearer", "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60}
