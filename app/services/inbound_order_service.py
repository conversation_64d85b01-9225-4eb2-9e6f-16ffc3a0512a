from typing import List

from fastapi_pagination import Page
from fastapi_pagination.ext.tortoise import apaginate
from tortoise.functions import Count, Sum

from app.core.exceptions import BusinessException
from app.filters.inbound_order_filters import InboundOrderFilterParams
from app.models import (
    InboundOrder,
    InboundOrderDetail,
    InboundOrderStatusEnum,
    User,
)
from app.schemas.inbound_order_schemas import (
    InboundOrderCreateRequest,
    InboundOrderInfoPydantic,
    InboundOrderListResponse,
    InboundOrderUpdateRequest,
)


class InboundOrderService:
    @staticmethod
    async def create(create_data: InboundOrderCreateRequest, current_user: User):
        """
        创建入库记录
        """

        if not current_user.company_id:
            raise BusinessException(msg="当前用户所属公司不存在")

        check_user = await User.get_or_none(user_id=create_data.check_user_id, company_id=current_user.company_id, is_deleted=False)
        if not check_user:
            raise BusinessException(msg="清点人员不存在")

        create_data.check_user_id = check_user.id
        new_inbound_order = await InboundOrder.create(
            **create_data.model_dump(),
            company_id=current_user.company_id,
            create_user_id=current_user.id,
        )

        # 重新赋值check_user_id
        new_inbound_order.check_user_id = check_user.user_id
        new_inbound_order.check_user_name = check_user.username
        return await InboundOrderInfoPydantic.from_tortoise_orm(new_inbound_order)

    @staticmethod
    async def list(
        filter_query: InboundOrderFilterParams,
        current_user: User,
    ) -> Page[InboundOrderListResponse]:
        """
        获取入库记录列表
        """

        query = InboundOrder.filter(is_deleted=False, company_id=current_user.company_id).prefetch_related("check_user").order_by("-id")
        queryset = await filter_query.filter_queryset(query)
        paginate_data = await apaginate(queryset, filter_query)

        # 获取入库单详情
        detail_data = (
            await InboundOrderDetail.filter(
                inbound_order_id__in=[item.id for item in paginate_data.items],
                is_deleted=False,
            )
            .annotate(
                detail_count=Count("id"),
                has_check_product_count=Sum("product_count"),
                has_check_cert_count=Sum("cert_count"),
                has_check_waybill_count=Sum("batch_count"),
            )
            .group_by("inbound_order_id")
            .values(
                "inbound_order_id",
                "detail_count",
                "has_check_product_count",
                "has_check_cert_count",
                "has_check_waybill_count",
            )
        )
        detail_data_map = {item["inbound_order_id"]: item for item in detail_data}

        for item in paginate_data.items:
            # 该入库单下有多少个任务单
            item.each_count = detail_data_map.get(item.id, {}).get("detail_count", 0)
            # 面单数量除于分批数量
            item.per_batch_count = item.waybill_count // item.each_count if item.each_count > 0 else 0
            # 已清点商品数量
            item.has_check_product_count = detail_data_map.get(item.id, {}).get("has_check_product_count", 0)
            # 已清点证书数量
            item.has_check_cert_count = detail_data_map.get(item.id, {}).get("has_check_cert_count", 0)
            # 已清点面单数量
            item.has_check_waybill_count = detail_data_map.get(item.id, {}).get("has_check_waybill_count", 0)
            # 未清点面单数量
            item.remaining_waybill_count = item.waybill_count - item.has_check_waybill_count
            # 清点人员名称
            item.check_user_name = item.check_user.username
            # 清点人员user_id
            item.check_user_id = item.check_user.user_id

        return paginate_data

    @staticmethod
    async def detail(code: str, current_user: User):
        """
        获取入库记录详情
        """
        inbound_order = await InboundOrder.get_or_none(code=code, is_deleted=False, company_id=current_user.company_id)
        if not inbound_order:
            raise BusinessException(msg="入库记录不存在")
        return await InboundOrderInfoPydantic.from_tortoise_orm(inbound_order)

    @staticmethod
    async def update(code: str, update_data: InboundOrderUpdateRequest, current_user: User):
        """
        更新入库记录
        """
        inbound_order = await InboundOrder.get_or_none(code=code, is_deleted=False, company_id=current_user.company_id)
        if not inbound_order:
            raise BusinessException(msg="入库记录不存在")

        # 判断check user_id是否存在
        check_user = await User.get_or_none(user_id=update_data.check_user_id, company_id=current_user.company_id, is_deleted=False)
        if not check_user:
            raise BusinessException(msg="清点人员不存在")
        # 重新赋值check_user_id
        update_data.check_user_id = check_user.id

        await inbound_order.update_from_dict(update_data.model_dump())
        inbound_order.update_user_id = current_user.id
        await inbound_order.save()
        return await InboundOrderInfoPydantic.from_tortoise_orm(inbound_order)

    @staticmethod
    async def delete(code: str, current_user: User):
        """
        删除入库记录
        """
        inbound_order = await InboundOrder.get_or_none(code=code, is_deleted=False, company_id=current_user.company_id)
        if not inbound_order:
            raise BusinessException(msg="入库记录不存在")

        if inbound_order.status == InboundOrderStatusEnum.SHIPPING.value:
            raise BusinessException(msg="发货中的入库记录不能删除")

        if inbound_order.status == InboundOrderStatusEnum.COMPLETED.value:
            raise BusinessException(msg="已完成的入库记录不能删除")

        inbound_order.is_deleted = True
        inbound_order.update_user_id = current_user.id
        await inbound_order.save()

    @staticmethod
    async def batch_delete(code_list: List[str], current_user: User):
        """
        批量删除入库记录
        """
        await InboundOrder.filter(
            code__in=code_list,
            is_deleted=False,
            company_id=current_user.company_id,
            status__in=[InboundOrderStatusEnum.PENDING_SCAN.value, InboundOrderStatusEnum.PENDING_SHIP.value],
        ).update(is_deleted=True, update_user_id=current_user.id)
