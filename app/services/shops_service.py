from fastapi_pagination.ext.tortoise import apaginate

from app.core.exceptions import BusinessException
from app.filters.common_filters import ShopFilterParams
from app.models.common_models import Shops
from app.models.user_models import User
from app.schemas.shops_schemas import BulkCreateShopPydantic, ShopPydantic


class ShopService:
    @staticmethod
    async def create_shop(create_data: ShopPydantic, current_user: User):
        if await Shops.get_or_none(shop_id=create_data.shop_id):
            raise BusinessException(msg="店铺id已存在")

        shop = Shops(
            shop_id=create_data.shop_id,
            shop_name=create_data.shop_name,
            company_id=current_user.company_id,
            create_user_id=current_user.id,
        )
        await shop.save()
        return shop

    @staticmethod
    async def update_shop(shop_id: str, update_data: ShopPydantic, current_user: User):
        shop = await Shops.get_or_none(shop_id=shop_id)
        if not shop:
            raise BusinessException(msg="店铺不存在")

        shop.shop_name = update_data.shop_name
        shop.update_user_id = current_user.id
        await shop.save()
        return shop

    @staticmethod
    async def delete_shop(shop_id: str, current_user: User):
        shop = await Shops.get_or_none(shop_id=shop_id)
        if not shop:
            raise BusinessException(msg="店铺不存在")

        shop.is_deleted = True
        shop.update_user_id = current_user.id
        await shop.save()

    @staticmethod
    async def get_shop_list(filter_query: ShopFilterParams, current_user: User):
        # 加分页
        shops = await Shops.filter(company_id=current_user.company_id).all()

        shops = await apaginate(shops, filter_query)

        return shops

    @staticmethod
    async def get_shop_enum(current_user: User) -> list[Shops]:
        return await Shops.filter(company_id=current_user.company_id, is_deleted=False).all()

    @staticmethod
    async def batch_create_shop(create_shops_data: BulkCreateShopPydantic, current_user: User):
        # 批量查询店铺id是否存在，在的话就走更新逻辑，不在的话就走创建逻辑
        shop_ids = [shop.shop_id for shop in create_shops_data.shops]
        exist_shops = await Shops.filter(shop_id__in=shop_ids).all()
        exist_shop_ids = [shop.shop_id for shop in exist_shops]
        # 批量创建店铺 或者 批量更新店铺 bulk_create/bulk_update
        need_update_shops = []
        need_create_shops = []
        for create_shop in create_shops_data.shops:
            if create_shop.shop_id in exist_shop_ids:
                create_shop.shop_name = create_shop.shop_name
                create_shop.update_user_id = current_user.id
                need_update_shops.append(create_shop)
            else:
                need_create_shops.append(create_shop)

        if need_update_shops:
            await Shops.bulk_update(need_update_shops, fields=["shop_name", "update_user_id"])
        if need_create_shops:
            await Shops.bulk_create(need_create_shops)
        return None
