from fastapi_pagination import Page
from fastapi_pagination.ext.tortoise import apaginate
from tortoise.query_utils import Prefetch
from tortoise.transactions import in_transaction

from app.core.exceptions import BusinessException
from app.filters.inbound_order_detail_filters import InboundOrderDetailFilterParams
from app.models import (
    CertTypeEnum,
    InboundOrder,
    InboundOrderDetail,
    ShippingSubscribe,
    ShippingWaybill,
    User,
    WaybillProduct,
)
from app.schemas.inbound_order_detail_schemas import (
    InboundOrderDetailBatchUpdateRequest,
    InboundOrderDetailCreateRequest,
    InboundOrderDetailInfoResponse,
)


class InboundOrderDetailService:
    @staticmethod
    async def _get_inbound_order(code: str, current_user: User) -> InboundOrder:
        inbound_order = await InboundOrder.get_or_none(code=code, is_deleted=False, company_id=current_user.company_id)
        if not inbound_order:
            raise BusinessException(msg="入库记录不存在")
        return inbound_order

    @staticmethod
    async def create(code: str, create_data: InboundOrderDetailCreateRequest, current_user: User) -> InboundOrderDetail:
        """创建入库记录清点明细

        Args:
            code (str): 入库单号
            create_data (InboundOrderDetailCreateRequest): 创建数据
            current_user (User): 当前用户

        Raises:
            BusinessException: 入库记录不存在
        """
        inbound_order = await InboundOrderDetailService._get_inbound_order(code, current_user)

        if await inbound_order.current_remaining_waybill_count == 0:
            raise BusinessException(msg="面单已分配完毕")

        # 判断面单余量是否大于0
        if await inbound_order.current_remaining_waybill_count < create_data.batch_count:
            raise BusinessException(msg="面单余量不足")

        # 一物一证、随机发证两中发证方式时，校验依据使用“商品数量”作为依据
        if (
            create_data.cert_type
            in [
                CertTypeEnum.SINGLE.value,
                CertTypeEnum.RANDOM.value,
            ]
            and create_data.product_count != create_data.cert_count
        ):
            raise BusinessException(msg="与面单需求商品数量不一致！")

        # 1. 判断user是否存在, 重新赋值主键

        shipping_user = None
        if create_data.shipping_user_id:
            user = await User.get_or_none(user_id=create_data.shipping_user_id, company_id=current_user.company_id, is_deleted=False)
            if not user:
                raise BusinessException(msg="发货人不存在")
            shipping_user = user
            create_data.shipping_user_id = user.id

        # 2. 判断单号是否已被使用（检查是否已经分配给其他明细）
        exist_waybills = (
            await ShippingWaybill.filter(
                waybill_code__in=create_data.waybill_codes,
                is_deleted=False,
            )
            .only("waybill_code")
            .all()
        )
        exist_waybill_codes_list = [waybill.waybill_code for waybill in exist_waybills]
        if exist_waybill_codes_list:
            raise BusinessException(msg="面单编号:{}已被分配".format(", ".join(exist_waybill_codes_list)))

        # 3. 找出不在订阅中的单号， # todo: 再检查是否在抖店的订阅中
        exist_subscribes = await ShippingSubscribe.filter(logistics_order_id__in=create_data.waybill_codes).all()
        exist_subscribes_map = {subscribe.logistics_order_id: subscribe for subscribe in exist_subscribes}

        not_exist_logistics_order_ids = list(set(create_data.waybill_codes) - set(exist_subscribes_map.keys()))
        if not_exist_logistics_order_ids:
            raise BusinessException(msg="面单编号:{}不在订阅中".format(", ".join(not_exist_logistics_order_ids)))

        # 判断面单数量和扫描到的面单数量是否一致
        if len(create_data.waybill_codes) != create_data.batch_count:
            raise BusinessException(msg="面单数量和入库单面单数量不一致")

        # 判断商品数量, 从扫面面单关联的订单中所有的商品数量总和
        if create_data.product_count != sum([subscribe.total_qty for subscribe in exist_subscribes]):
            raise BusinessException(msg="<UNK>")

        async with in_transaction("default"):
            new_inbound_detail = await InboundOrderDetail.create(
                inbound_order_id=inbound_order.id,
                company_id=current_user.company_id,
                create_user_id=current_user.id,
                **create_data.model_dump(exclude={"waybill_codes"}),
            )

            # 批量创建面单和商品关系
            for waybill_code in create_data.waybill_codes:
                # 1. 从订阅表获取面单信息
                subscribe = exist_subscribes_map[waybill_code]
                # 2. 创建面单记录
                waybill = await ShippingWaybill.create(
                    waybill_code=waybill_code,
                    inbound_order_id=inbound_order.id,
                    inbound_order_detail_id=new_inbound_detail.id,
                    company_id=current_user.company_id,
                    shipping_user_id=new_inbound_detail.shipping_user_id,
                    create_user_id=current_user.id,
                    # 订单信息
                    order_id=subscribe.order_id,
                    logistics_company=subscribe.logistics_company,
                    send_date=subscribe.send_date,
                    total_qty=subscribe.total_qty,
                    # 冗余字段
                    inbound_order_code=inbound_order.code,
                    inbound_order_detail_code=new_inbound_detail.code,
                    shipping_user_name=shipping_user.username if shipping_user else None,
                )

                # 3. 创建面单商品记录
                if subscribe.items:
                    for item in subscribe.items:
                        from app.models import ItemTypeEnum

                        await WaybillProduct.create(
                            waybill_id=waybill.id,
                            company_id=current_user.company_id,
                            inbound_order_id=inbound_order.id,
                            inbound_order_detail_id=new_inbound_detail.id,
                            create_user_id=current_user.id,
                            # 记录类型
                            item_type=ItemTypeEnum.PRODUCT_ONLY.value,
                            # 商品信息
                            sku_id=item.get("sku_id"),
                            product_name=item.get("name"),
                            quantity=item.get("qty", 1),
                            outer_oi_id=item.get("outer_oi_id"),
                            oi_id=item.get("oi_id"),
                            # 冗余字段
                            waybill_code=waybill_code,
                            order_id=subscribe.order_id,
                        )

            # 更新状态
            inbound_order.status = await inbound_order.current_status
            await inbound_order.save(update_fields=["status"])

        return new_inbound_detail

    @staticmethod
    async def list(code: str, filter_query: InboundOrderDetailFilterParams, current_user: User) -> Page[InboundOrderDetailInfoResponse]:
        inbound_order = await InboundOrderDetailService._get_inbound_order(code, current_user)
        waybills_prefetch = Prefetch("waybills_set", ShippingWaybill.filter(is_deleted=False))
        details = (
            InboundOrderDetail.filter(
                inbound_order=inbound_order,
                is_deleted=False,
            )
            .prefetch_related("shipping_user", waybills_prefetch)
            .order_by("-id")
        )
        queryset = await filter_query.filter_queryset(details)
        paginate_data = await apaginate(queryset, filter_query)

        for item in paginate_data.items:
            # 重新赋值，命名为waybill_code_list防止字段冲突
            item.waybill_codes = [waybill_code.waybill_code for waybill_code in await item.waybills_set.all()]
            item.shipping_user_id = item.shipping_user.user_id if item.shipping_user else None
            item.shipping_user_name = item.shipping_user.username if item.shipping_user else ""

        return paginate_data

    @staticmethod
    async def detail(code: str, detail_code: str, current_user: User) -> InboundOrderDetail:
        # 获取入库记录清点明细
        inbound_order = await InboundOrderDetailService._get_inbound_order(code, current_user)
        detail = await InboundOrderDetail.get_or_none(
            inbound_order=inbound_order,
            code=detail_code,
            is_deleted=False,
            company_id=current_user.company_id,
        )
        if not detail:
            raise BusinessException(msg="入库记录清点明细不存在")

        shipping_user = await detail.shipping_user

        shipping_user = shipping_user if shipping_user else None
        detail.shipping_user_id = shipping_user.user_id if shipping_user else None
        detail.shipping_user_name = shipping_user.username if shipping_user else ""
        detail.waybill_codes = [waybill_code.waybill_code for waybill_code in await detail.waybills_set.all()]

        return detail

    @staticmethod
    async def delete(code: str, detail_code: str, current_user: User) -> None:
        """删除入库记录清点明细

        Args:
            code (str): 入库单号
            detail_code (str): 入库记录清点明细号
            current_user (User): 当前用户
        """
        detail = await InboundOrderDetailService.detail(code, detail_code, current_user)

        async with in_transaction("default"):
            detail.is_deleted = True
            detail.update_user_id = current_user.id

            # 删除面单
            await detail.waybills_set.all().update(is_deleted=True, update_user_id=current_user.id)

            await detail.save()

    @staticmethod
    async def batch_update(code: str, detail_code: str, update_data: InboundOrderDetailBatchUpdateRequest, current_user: User) -> InboundOrderDetail:
        """局部修改入库记录清点明细
        只修改发货人

        Args:
            code (str): 入库单号
            detail_code (str): 入库记录清点明细号
            update_data (InboundOrderDetailBatchUpdateRequest): 更新数据
            current_user (User): 当前用户

        Raises:
            BusinessException: 入库记录清点明细不存在

        Returns:
            InboundOrderDetail: 更新后的入库记录清点明细
        """
        detail = await InboundOrderDetailService.detail(code, detail_code, current_user)

        shipping_user = await User.get_or_none(
            user_id=update_data.shipping_user_id,
            company_id=current_user.company_id,
            is_deleted=False,
        )
        if not shipping_user:
            raise BusinessException(msg="发货人不存在")

        has_change_user = False
        if shipping_user.id != detail.shipping_user_id:
            has_change_user = True

        async with in_transaction("default"):
            detail.update_user_id = current_user.id
            detail.shipping_user_id = shipping_user.id
            await detail.save()

            # 修改关联的面单发货人
            if has_change_user:
                await detail.waybills_set.filter(is_deleted=False).update(
                    is_deleted=True,
                    update_user_id=current_user.id,
                    shipping_user_id=update_data.shipping_user_id,
                )

        detail.shipping_user_id = shipping_user.user_id if shipping_user.user_id else None
        detail.shipping_user_name = shipping_user.username if shipping_user else ""
        detail.waybill_codes = [waybill_code.waybill_code for waybill_code in await detail.waybills_set.all()]
        return detail
