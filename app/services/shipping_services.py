# -*- coding: utf-8 -*-
from datetime import datetime

from fastapi_pagination.ext.tortoise import apaginate
from tortoise.expressions import F

from app.core.exceptions import BusinessException
from app.filters.shipping_filters import PendingShippingFilterParams
from app.models import User, WaybillProduct
from app.schemas.shipping_schemas import WayBillProductBindCertRequest


class ShippingOrderService:
    @staticmethod
    async def get_pending_shipping_orders(current_user: User, filter_query: PendingShippingFilterParams):
        query = (
            (
                WaybillProduct.filter(
                    is_deleted=False,
                    company_id=current_user.company_id,
                    waybill__shipping_user_id=current_user.id,
                ).order_by("-id")
            )
            .prefetch_related(
                "inbound_order_detail",
            )
            .order_by("bind_status", "create_time")
        ).annotate(
            shipping_type=F("inbound_order_detail__shipping_type"),
            cert_type=F("inbound_order_detail__cert_type"),
        )
        queryset = await filter_query.filter_queryset(query)
        paginate_data = await apaginate(queryset, filter_query)

        return paginate_data

    @staticmethod
    async def bind_cert(bind_data: WayBillProductBindCertRequest, current_user: User):
        waybill_product = await WaybillProduct.get_or_none(
            id=bind_data.id,
            is_deleted=False,
            company_id=current_user.company_id,
            waybill__shipping_user_id=current_user.id,
        )
        if not waybill_product:
            raise BusinessException(msg="面单商品不存在")

        if waybill_product.cert_sn_code:
            raise BusinessException(msg="证书已绑定")

        waybill_product.cert_sn_code = bind_data.cert_sn_code
        waybill_product.product_sn_code = bind_data.product_sn_code
        waybill_product.bind_status = 2
        waybill_product.bind_time = datetime.now()
        waybill_product.cert_type = 1
        await waybill_product.save()

        return
