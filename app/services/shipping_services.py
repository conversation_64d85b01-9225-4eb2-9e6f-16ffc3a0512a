# -*- coding: utf-8 -*-
from fastapi_pagination.ext.tortoise import apaginate

from app.filters.shipping_filters import PendingShippingFilterParams
from app.models import User, WaybillProduct


class ShippingOrderService:
    @staticmethod
    async def get_pending_shipping_orders(current_user: User, filter_query: PendingShippingFilterParams):
        query = (
            WaybillProduct.filter(
                is_deleted=False,
                company_id=current_user.company_id,
                waybill__shipping_user_id=current_user.id,
            )
            .prefetch_related("check_user")
            .order_by("-id")
        ).order_by("bind_status", "create_time")
        queryset = await filter_query.filter_queryset(query)
        paginate_data = await apaginate(queryset, filter_query)
        return paginate_data
