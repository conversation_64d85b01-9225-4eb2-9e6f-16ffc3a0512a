# -*- coding: utf-8 -*-
from app.models import User, WaybillProduct, OrdersOrderitems


class OrderService:
    @staticmethod
    async def get_orders(current_user: User):
        # 获取当前用户绑定的面单
        waybill_product_relates = await WaybillProduct.filter(
            bind_status=2,
            is_deleted=False,
            company_id=current_user.company_id,
            inbound_order_detail__shipping_user_id=current_user.id,
        )

        # 根据order_id跟sku_id join orders_orderitems表
        order_items = await OrdersOrderitems.filter(
            order_id__in=waybill_product_relates.values_list("order_id", flat=True),
            sku_id__in=waybill_product_relates.values_list("sku_id", flat=True),
        ).all()

        # 根据order_items的order_id join orders_orders表
        orders = await OrdersOrders.filter(
            order_id__in=order_items.values_list("order_id", flat=True),
        ).all()

        return orders
