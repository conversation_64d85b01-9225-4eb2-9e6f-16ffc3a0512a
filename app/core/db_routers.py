# -*- coding: utf-8 -*-
from typing import Type

from tortoise import Model


class DBRouter:
    """
    数据库路由器
    根据模型所属的应用来决定使用哪个数据库连接
    """

    def db_for_read(self, model: Type[Model]):
        """
        决定读取操作使用哪个数据库连接
        """
        # 获取模型的模块路径
        model_module = model.__module__

        # 如果是 orders 相关的模型，使用 orders 数据库
        if 'orders_models' in model_module or 'orders' in model_module:
            return "orders"

        # 默认使用 default 数据库
        return "default"

    def db_for_write(self, model: Type[Model]):
        """
        决定写入操作使用哪个数据库连接
        """
        # 获取模型的模块路径
        model_module = model.__module__

        # 如果是 orders 相关的模型，使用 orders 数据库
        if 'orders_models' in model_module or 'orders' in model_module:
            return "orders"

        # 默认使用 default 数据库
        return "default"
