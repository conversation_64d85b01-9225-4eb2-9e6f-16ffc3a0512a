# -*- coding: utf-8 -*-
class APIException(Exception):
    def __init__(self, msg, code):
        self.code = code
        self.msg = msg


class ParamsException(APIException):
    def __init__(self, msg: str = "参数错误", code: int = 400):
        self.code = code
        self.msg = msg


class InvalidTokenException(APIException):
    def __init__(self, msg: str = "凭证错误，请重新登录", code: int = 401):
        self.code = code
        self.msg = msg


class PermissionException(APIException):
    def __init__(self, msg: str = "无权访问", code: int = 403):
        self.code = code
        self.msg = msg


class BusinessException(APIException):
    def __init__(self, msg: str = "业务异常", code: int = 400):
        self.code = code
        self.msg = msg


class DataNotFoundException(APIException):
    def __init__(self, msg: str = "数据不存在", code: int = 404):
        self.code = code
        self.msg = msg
