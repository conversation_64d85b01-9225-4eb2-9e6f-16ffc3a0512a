from typing import Dict, List

from app.core.config import settings

# 定义模型模块列表
MODEL_MODULES = [
    "aerich.models",
    "app.models.user_models",
    "app.models.company_models",
    "app.models.sequence_models",
    "app.models.subscribe_models",
    "app.models.inbound_order_models",
]

ORDER_MODEL_MODULES = [
    "app.models.orders_models",
]


def get_tortoise_config() -> Dict[str, Dict]:
    """
    获取 Tortoise ORM 配置

    根据当前环境配置返回适当的 Tortoise ORM 配置
    包括数据库连接、连接池设置、模型定义和时区配置
    支持多数据库配置
    """
    # 默认数据库配置
    config = {
        "connections": {**settings.DATABASES},
        "apps": {
            "models": {
                "models": MODEL_MODULES,
                "default_connection": "default",
            },
            "orders": {
                "models": ORDER_MODEL_MODULES,
                "default_connection": "orders",
            },
        },
        "use_tz": False,
        "timezone": "Asia/Shanghai",
    }

    return config


# 简化的配置，用于 Aerich 迁移工具
if "default" not in settings.DATABASES:
    raise ValueError("default database not found in DATABASES")

default_connection_credentials = settings.DATABASES["default"]["credentials"]

TORTOISE_CONFIG = {
    "connections": {
        "default": "postgres://{user}:{password}@{host}:{port}/{database}".format(
            user=default_connection_credentials["user"],
            password=default_connection_credentials["password"],
            host=default_connection_credentials["host"],
            port=default_connection_credentials["port"],
            database=default_connection_credentials["database"],
        )
    },
    "apps": {
        "models": {
            "models": MODEL_MODULES,
            "default_connection": "default",
        },
        "orders": {
            "models": ORDER_MODEL_MODULES,
            "default_connection": "orders",
        },
    },
    # 配置好读写路由
    "routers": ["app.core.db_routers.DBRouter"],
}

# 全局 ORM 配置
TORTOISE_ORM = get_tortoise_config()


def get_db_models() -> List[str]:
    """
    获取数据库模型列表
    """
    return MODEL_MODULES
