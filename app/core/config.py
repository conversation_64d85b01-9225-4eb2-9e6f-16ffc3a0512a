from functools import lru_cache
from pathlib import Path

from pydantic_settings import BaseSettings

import local_settings


class Settings(BaseSettings):
    # API Settings
    API_V1_STR: str = local_settings.API_V1_STR
    PROJECT_NAME: str = local_settings.PROJECT_NAME
    DEBUG: bool = local_settings.DEBUG
    BACKEND_CORS_ORIGINS: list[str] = local_settings.BACKEND_CORS_ORIGINS
    PROJECT_ROOT: Path = local_settings.PROJECT_ROOT
    LOG_DIR: Path = local_settings.LOG_DIR

    # Database Settings
    DATABASES: dict[str, dict] = local_settings.DATABASES

    # Redis Settings
    REDIS_HOST: str = local_settings.REDIS_HOST
    REDIS_PORT: str = local_settings.REDIS_PORT
    REDIS_DB: str = local_settings.REDIS_DB
    REDIS_PASSWORD: str = local_settings.REDIS_PASSWORD
    REDIS_POOL_SIZE: int = local_settings.REDIS_POOL_SIZE
    REDIS_POOL_TIMEOUT: int = local_settings.REDIS_POOL_TIMEOUT

    # JWT Settings
    JWT_SECRET_KEY: str = local_settings.JWT_SECRET_KEY
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30  # 30 minutes
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7  # 7 days

    # Aliyun Settings
    ALIYUN_ACCESS_KEY_ID: str = local_settings.ALIYUN_ACCESS_KEY_ID
    ALIYUN_ACCESS_KEY_SECRET: str = local_settings.ALIYUN_ACCESS_KEY_SECRET

    # sms verify settings
    SMS_CODE_LENGTH: int = local_settings.SMS_CODE_LENGTH
    SMS_CODE_EXPIRE_MINUTES: int = local_settings.SMS_CODE_EXPIRE_MINUTES
    ALIYUN_SMS_SIGN_NAME: str = local_settings.ALIYUN_SMS_SIGN_NAME
    ALIYUN_SMS_TEMPLATE_CODE: str = local_settings.ALIYUN_SMS_TEMPLATE_CODE


@lru_cache()
def get_settings() -> Settings:
    """
    获取应用配置
    Returns:
        Settings: 配置对象
    """
    return Settings()


settings = get_settings()
