# 证书绑定系统最终数据库设计

## 设计理念

基于您的业务需求，采用三表结构设计证书绑定系统：

1. **一个 detail 包含多个面单号**
2. **一个面单号对应多个商品**（从 ShippingSubscribe 订阅表获取）
3. **证书可以绑定到面单（随机发证）或具体商品（一物一证）**
4. **支持快递单号、订单号、证书号、商品SN码的快速搜索**

## 数据库表结构

### 1. 面单表 (cert_shipping_waybill)

**功能**: 管理面单基础信息，一个面单一条记录

**关键字段**:
- `waybill_code` - 快递单号（唯一）
- `order_id` - 线上订单号
- `inbound_order_detail_id` - 关联入库单明细
- `shipping_user_id` - 发货人
- `logistics_company` - 物流公司
- `total_qty` - 总数量

**数据来源**: 
- 面单号来自 `inbound_order_detail.waybill_codes`
- 订单信息来自 `ShippingSubscribe` 订阅表

### 2. 面单商品表 (cert_waybill_product)

**功能**: 管理面单下的商品信息，一个商品一条记录

**关键字段**:
- `waybill_id` - 关联面单
- `sku_id` - 商品SKU
- `product_name` - 商品名称
- `quantity` - 数量
- `outer_oi_id` - 外部订单项ID

**数据来源**: 从 `ShippingSubscribe.items` 解析获取

### 3. 证书绑定表 (cert_certificate_binding)

**功能**: 管理证书绑定关系，一个证书一条记录

**关键字段**:
- `cert_sn_code` - 证书SN码（唯一）
- `waybill_id` - 关联面单
- `product_id` - 关联商品（一物一证时填充，随机发证时为空）
- `cert_type` - 证书类型（1:一物一证, 2:随机发证）
- `product_sn_code` - 商品SN码

## 业务流程

### 1. 创建入库单明细时的自动化流程

```
1. 用户创建 InboundOrderDetail，包含 waybill_codes 数组
2. 系统遍历每个 waybill_code：
   a. 从 ShippingSubscribe 表查询面单信息
   b. 创建 ShippingWaybill 记录
   c. 解析 items 数组，为每个商品创建 WaybillProduct 记录
3. 完成面单和商品关系的建立
```

### 2. 发货人查看待发货订单

```
1. 根据 shipping_user_id 查询分配给发货人的面单
2. 预加载面单下的商品信息和已绑定证书
3. 返回完整的面单、商品、证书信息
```

### 3. 证书绑定流程

#### 随机发证
```
1. 选择面单
2. 输入证书SN码
3. 创建 CertificateBinding 记录，product_id 为空
```

#### 一物一证
```
1. 选择面单下的具体商品
2. 输入证书SN码和商品SN码
3. 创建 CertificateBinding 记录，关联具体商品
```

## 快速查询支持

### 1. 按快递单号查询
```sql
-- 查询面单信息
SELECT * FROM cert_shipping_waybill WHERE waybill_code = ? AND company_id = ?

-- 查询面单商品
SELECT * FROM cert_waybill_product WHERE waybill_code = ? AND company_id = ?

-- 查询面单证书
SELECT * FROM cert_certificate_binding WHERE waybill_code = ? AND company_id = ?
```

### 2. 按证书号查询
```sql
SELECT cb.*, sw.*, wp.* 
FROM cert_certificate_binding cb
LEFT JOIN cert_shipping_waybill sw ON cb.waybill_id = sw.id
LEFT JOIN cert_waybill_product wp ON cb.product_id = wp.id
WHERE cb.cert_sn_code = ? AND cb.company_id = ?
```

### 3. 按商品SN码查询
```sql
SELECT * FROM cert_certificate_binding 
WHERE product_sn_code = ? AND company_id = ?
```

### 4. 按订单号查询
```sql
SELECT * FROM cert_certificate_binding 
WHERE order_id = ? AND company_id = ?
```

## API接口设计

### 1. 获取用户待发货面单
```
GET /api/v1/certificate-binding/user/pending-waybills
```
返回分配给当前用户的面单列表，包含商品信息和已绑定证书数量

### 2. 获取面单详细信息
```
GET /api/v1/certificate-binding/waybill/{waybill_code}/detail
```
返回面单信息和商品列表

### 3. 随机发证
```
POST /api/v1/certificate-binding/bind/random
{
  "waybill_code": "string",
  "cert_sn_code": "string"
}
```

### 4. 一物一证
```
POST /api/v1/certificate-binding/bind/product
{
  "waybill_code": "string",
  "cert_sn_code": "string",
  "product_id": 123,
  "product_sn_code": "string"
}
```

### 5. 多维度搜索
```
GET /api/v1/certificate-binding/search?search_term=xxx&search_type=cert_sn_code
```
支持按证书号、商品SN码、订单号、面单号搜索

## 设计优势

### 1. 数据结构清晰
- **面单表**: 管理面单基础信息
- **商品表**: 管理面单下的商品明细
- **绑定表**: 管理证书绑定关系

### 2. 支持复杂业务场景
- ✅ 一个面单包含多个商品
- ✅ 随机发证（证书绑定到面单）
- ✅ 一物一证（证书绑定到具体商品）
- ✅ 商品SN码管理

### 3. 高效查询性能
- 通过冗余字段避免复杂JOIN
- 针对常用查询场景设计索引
- 支持多维度快速搜索

### 4. 自动化流程
- 创建明细时自动建立面单商品关系
- 从订阅表自动获取商品信息
- 减少手工操作，提高效率

## 数据流向图

```
InboundOrderDetail.waybill_codes
         ↓
ShippingSubscribe (查询商品信息)
         ↓
ShippingWaybill (创建面单)
         ↓
WaybillProduct (创建商品)
         ↓
CertificateBinding (绑定证书)
```

## 索引设计

### ShippingWaybill 表索引
- `waybill_code + company_id` (唯一查询)
- `shipping_user_id + company_id` (发货人查询)
- `order_id + company_id` (订单查询)

### WaybillProduct 表索引
- `waybill_id` (面单商品查询)
- `sku_id + company_id` (SKU查询)
- `waybill_code + company_id` (冗余查询)

### CertificateBinding 表索引
- `cert_sn_code + company_id` (证书查询)
- `waybill_code + company_id` (面单证书查询)
- `product_sn_code + company_id` (商品SN查询)
- `order_id + company_id` (订单证书查询)

这个设计完全满足您的业务需求，支持灵活的证书绑定方式，并且通过合理的数据结构和索引设计保证了查询性能。
