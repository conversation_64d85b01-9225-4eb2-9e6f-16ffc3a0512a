# 合并后的证书绑定系统数据库设计

## 设计理念

将 `CertificateBinding` 合并到 `WaybillProduct` 中，通过 `item_type` 字段区分不同类型的记录，简化数据结构。

## 数据库表结构

### 1. 面单表 (cert_shipping_waybill)

**功能**: 管理面单基础信息，一个面单一条记录

**关键字段**:
- `waybill_code` - 快递单号（唯一）
- `order_id` - 线上订单号
- `inbound_order_detail_id` - 关联入库单明细
- `shipping_user_id` - 发货人

### 2. 面单商品表 (cert_waybill_product) - 合并设计

**功能**: 统一管理商品信息和证书绑定，通过 `item_type` 区分记录类型

**记录类型**:
1. **仅商品记录** (`item_type=1`): 只有商品信息，证书字段为空
2. **商品+证书记录** (`item_type=2`): 一物一证，商品和证书信息都有
3. **随机发证记录** (`item_type=3`): 只有证书信息，商品字段为空

**关键字段**:
- `item_type` - 记录类型（1:仅商品, 2:商品+证书, 3:随机发证）
- `sku_id` - 商品SKU（商品记录时必填）
- `product_name` - 商品名称
- `quantity` - 商品数量
- `cert_sn_code` - 证书SN码（证书记录时必填，唯一）
- `cert_type` - 证书类型（1:一物一证, 2:随机发证）
- `product_sn_code` - 商品SN码
- `bind_time` - 绑定时间

## 业务场景示例

### 场景1：面单有3个商品，暂未绑定证书
```
waybill_id=1, item_type=1, sku_id="SKU001", product_name="商品1", cert_sn_code=null
waybill_id=1, item_type=1, sku_id="SKU002", product_name="商品2", cert_sn_code=null  
waybill_id=1, item_type=1, sku_id="SKU003", product_name="商品3", cert_sn_code=null
```

### 场景2：面单有3个商品，一物一证绑定3个证书
```
waybill_id=1, item_type=2, sku_id="SKU001", product_name="商品1", cert_sn_code="CERT001"
waybill_id=1, item_type=2, sku_id="SKU002", product_name="商品2", cert_sn_code="CERT002"
waybill_id=1, item_type=2, sku_id="SKU003", product_name="商品3", cert_sn_code="CERT003"
```

### 场景3：面单有3个商品，随机发证1个证书
```
waybill_id=1, item_type=1, sku_id="SKU001", product_name="商品1", cert_sn_code=null
waybill_id=1, item_type=1, sku_id="SKU002", product_name="商品2", cert_sn_code=null
waybill_id=1, item_type=1, sku_id="SKU003", product_name="商品3", cert_sn_code=null
waybill_id=1, item_type=3, sku_id=null, product_name=null, cert_sn_code="CERT001"
```

### 场景4：混合模式 - 2个商品一物一证，1个随机发证
```
waybill_id=1, item_type=2, sku_id="SKU001", product_name="商品1", cert_sn_code="CERT001"
waybill_id=1, item_type=2, sku_id="SKU002", product_name="商品2", cert_sn_code="CERT002"
waybill_id=1, item_type=1, sku_id="SKU003", product_name="商品3", cert_sn_code=null
waybill_id=1, item_type=3, sku_id=null, product_name=null, cert_sn_code="CERT003"
```

## 业务流程

### 1. 创建入库单明细时
```
1. 遍历 waybill_codes 数组
2. 从 ShippingSubscribe 获取面单信息
3. 创建 ShippingWaybill 记录
4. 为每个商品创建 WaybillProduct 记录（item_type=1）
```

### 2. 证书绑定流程

#### 随机发证
```
1. 创建新的 WaybillProduct 记录
2. item_type=3, cert_sn_code=证书号, 商品字段为空
```

#### 一物一证
```
1. 找到目标商品记录（item_type=1）
2. 更新记录：item_type=2, cert_sn_code=证书号, 添加绑定时间
```

### 3. 解绑证书
```
- 随机发证记录：直接软删除
- 商品+证书记录：清空证书字段，item_type改回1
```

## 查询优化

### 1. 获取面单商品列表
```sql
SELECT * FROM cert_waybill_product 
WHERE waybill_id = ? AND item_type IN (1, 2) 
ORDER BY item_type, id
```

### 2. 获取面单证书列表
```sql
SELECT * FROM cert_waybill_product 
WHERE waybill_id = ? AND item_type IN (2, 3) AND cert_sn_code IS NOT NULL
ORDER BY bind_time
```

### 3. 按证书号查询
```sql
SELECT * FROM cert_waybill_product 
WHERE cert_sn_code = ? AND company_id = ?
```

### 4. 按商品SN码查询
```sql
SELECT * FROM cert_waybill_product 
WHERE product_sn_code = ? AND company_id = ?
```

## 索引设计

```sql
-- 记录类型查询
INDEX (item_type, company_id)

-- 证书查询
INDEX (cert_sn_code, company_id)

-- 商品查询
INDEX (sku_id, company_id)
INDEX (product_sn_code, company_id)

-- 面单查询
INDEX (waybill_id, item_type)
INDEX (waybill_code, company_id)

-- 订单查询
INDEX (order_id, company_id)
```

## API接口

### 1. 获取面单详情
```
GET /api/v1/certificate-binding/waybill/{waybill_code}/detail
```
返回面单信息、商品列表、证书列表

### 2. 随机发证
```
POST /api/v1/certificate-binding/bind/random
{
  "waybill_code": "string",
  "cert_sn_code": "string"
}
```

### 3. 一物一证
```
POST /api/v1/certificate-binding/bind/product
{
  "waybill_code": "string",
  "cert_sn_code": "string",
  "product_id": 123,
  "product_sn_code": "string"
}
```

## 设计优势

### ✅ 优点
1. **数据结构简化** - 只需要两个表
2. **减少JOIN查询** - 商品和证书信息在同一表中
3. **灵活的业务支持** - 通过 item_type 支持多种绑定模式
4. **查询性能好** - 通过索引优化常用查询

### ⚠️ 注意事项
1. **空值处理** - 需要根据 item_type 正确处理空值字段
2. **数据一致性** - 需要在应用层保证 item_type 与字段值的一致性
3. **查询复杂性** - 某些查询需要根据 item_type 进行过滤

## 枚举定义

### ItemTypeEnum
- 1: 仅商品
- 2: 商品+证书  
- 3: 随机发证

### CertificateTypeEnum
- 1: 一物一证
- 2: 随机发证

这个合并设计在保持功能完整性的同时，简化了数据结构，减少了表的数量，提高了查询效率。
