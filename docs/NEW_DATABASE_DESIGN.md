# 证书绑定系统数据库设计

## 设计理念

基于您的业务需求，设计了简化的证书绑定系统：

1. **一个 detail 包含多个面单号**
2. **一个面单号对应多个商品**（从 ShippingSubscribe 订阅表获取）
3. **证书可以绑定到面单（随机发证）或具体商品（一物一证）**
4. **支持快递单号、订单号、证书号、商品SN码的快速搜索**

## 数据库表结构

### 1. 核心业务表

#### 1.1 入库单主表 (cert_inbound_order)
- **功能**: 管理入库单基础信息
- **关键字段**: code(入库单号), company_id, check_user_id, status
- **索引**: code + company_id

#### 1.2 入库单明细表 (cert_inbound_order_detail) 
- **功能**: 任务分配给发货用户，包含发货类型和发证方式
- **关键字段**: code(任务单号), shipping_type, cert_type, shipping_user_id
- **关联**: inbound_order_id → cert_inbound_order.id
- **索引**: code + company_id, shipping_user_id

### 2. 新的发货管理表

#### 2.1 发货面单表 (cert_shipping_waybill)
- **功能**: 管理面单基础信息和状态
- **关键字段**: 
  - waybill_code (快递单号, 唯一)
  - order_id (线上订单号)
  - status (1:待发货, 2:已发货, 3:已完成)
  - shipping_user_id (发货人)
- **关联**: 
  - inbound_order_detail_id → cert_inbound_order_detail.id
  - company_id → cert_companies.id
- **索引**: 
  - waybill_code + company_id (主要查询)
  - order_id + company_id
  - shipping_user_id + status

#### 2.2 发货商品项目表 (cert_shipping_item)
- **功能**: 管理面单下的具体商品项目
- **关键字段**:
  - sku_id (商品SKU)
  - product_name (商品名称)
  - product_sn_code (商品SN码)
  - quantity (数量)
- **关联**: 
  - waybill_id → cert_shipping_waybill.id
  - company_id → cert_companies.id
- **冗余字段**: waybill_code, order_id (用于快速查询)
- **索引**:
  - sku_id + company_id
  - product_sn_code + company_id
  - waybill_code + company_id (冗余索引)

#### 2.3 发货证书表 (cert_shipping_certificate)
- **功能**: 管理面单下绑定的证书
- **关键字段**:
  - cert_sn_code (证书SN码, 唯一)
  - cert_type (1:一物一证, 2:随机发证)
  - bind_time (绑定时间)
- **关联**:
  - waybill_id → cert_shipping_waybill.id
  - item_id → cert_shipping_item.id (可为空，支持随机发证)
  - company_id → cert_companies.id
- **冗余字段**: waybill_code, order_id, product_sn_code (用于快速查询)
- **索引**:
  - cert_sn_code + company_id (主要查询)
  - waybill_code + company_id (冗余索引)
  - product_sn_code + company_id (冗余索引)

## 业务流程

### 1. 入库单创建流程
```
1. 创建入库单 (InboundOrder)
2. 创建任务明细，分配给发货用户 (InboundOrderDetail)
3. 发货用户在"待发货页面"看到分配给自己的任务
```

### 2. 发货绑定流程
```
1. 发货用户创建面单 (ShippingWaybill)
2. 添加商品项目到面单 (ShippingItem)
3. 绑定证书到面单/商品 (ShippingCertificate)
4. 更新面单状态为"已发货"
```

### 3. 快速查询支持

#### 3.1 按快递单号查询
```sql
-- 查询面单基础信息
SELECT * FROM cert_shipping_waybill WHERE waybill_code = ? AND company_id = ?

-- 查询面单下的商品
SELECT * FROM cert_shipping_item WHERE waybill_code = ? AND company_id = ?

-- 查询面单下的证书
SELECT * FROM cert_shipping_certificate WHERE waybill_code = ? AND company_id = ?
```

#### 3.2 按订单号查询
```sql
-- 通过冗余字段快速查询
SELECT * FROM cert_shipping_waybill WHERE order_id = ? AND company_id = ?
SELECT * FROM cert_shipping_item WHERE order_id = ? AND company_id = ?
SELECT * FROM cert_shipping_certificate WHERE order_id = ? AND company_id = ?
```

#### 3.3 按证书号查询
```sql
-- 直接查询证书表
SELECT * FROM cert_shipping_certificate WHERE cert_sn_code = ? AND company_id = ?

-- 关联查询获取完整信息
SELECT w.*, c.* FROM cert_shipping_certificate c
JOIN cert_shipping_waybill w ON c.waybill_id = w.id
WHERE c.cert_sn_code = ? AND c.company_id = ?
```

#### 3.4 按商品SN码查询
```sql
-- 通过商品表查询
SELECT * FROM cert_shipping_item WHERE product_sn_code = ? AND company_id = ?

-- 通过证书表的冗余字段查询
SELECT * FROM cert_shipping_certificate WHERE product_sn_code = ? AND company_id = ?
```

## 设计优势

### 1. 清晰的层次结构
- **入库单** → **任务明细** → **面单** → **商品项目/证书**
- 每一层都有明确的职责和关联关系

### 2. 灵活的证书绑定
- 支持一物一证：证书绑定到具体商品项目
- 支持随机发证：证书只绑定到面单，不绑定具体商品

### 3. 高效的查询性能
- 通过冗余字段避免复杂的JOIN查询
- 针对常用查询场景设计了复合索引
- 支持多维度快速搜索

### 4. 数据一致性
- 通过外键关联保证数据完整性
- 冗余字段通过应用层维护一致性

## 枚举定义

### 面单状态 (WaybillStatusEnum)
- 1: 待发货
- 2: 已发货  
- 3: 已完成

### 证书类型 (CertificateTypeEnum)
- 1: 一物一证
- 2: 随机发证

## 迁移建议

由于这是新项目，建议：
1. 删除旧的 `cert_waybill_code` 表
2. 创建新的三个表：`cert_shipping_waybill`, `cert_shipping_item`, `cert_shipping_certificate`
3. 更新相关的服务类和API接口
4. 编写数据迁移脚本（如果有历史数据需要迁移）
