# -*- coding: utf-8 -*-
import asyncio
import getpass
import os
import re
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


async def create_superuser():
    from tortoise import Tortoise

    from app.core.tortoise_config import TORTOISE_ORM
    from app.models.user_models import User

    # Initialize Tortoise ORM
    await Tortoise.init(TORTOISE_ORM, modules={"models": ["models.user"]})
    # Create database tables
    await Tortoise.generate_schemas()

    try:
        # 获取用户名
        print("\n--- 用户信息设置 ---")
        while True:
            username = input("请输入用户名 (3-20位字符): ").strip()

            # 检查用户名是否为空
            if not username:
                print("❌ 用户名不能为空，请重新输入")
                continue

            # 检查用户名长度
            if len(username) < 3:
                print("❌ 用户名长度至少3位，请重新输入")
                continue

            if len(username) > 20:
                print("❌ 用户名长度不能超过20位，请重新输入")
                continue

            # 检查用户名格式（可选：只允许字母、数字、下划线）
            import re
            if not re.match(r'^[a-zA-Z0-9_]+$', username):
                print("❌ 用户名只能包含字母、数字和下划线，请重新输入")
                continue

            print(f"✅ 用户名设置为: {username}")
            break

        # 检查用户是否已存在
        user = await User.get_or_none(username=username)
        if user:
            print(f"用户名 {username} 已存在")
            return

        # 获取密码（优化版：两次密码错误时重新输入密码）
        password_attempts = 0
        max_attempts = 3

        while password_attempts < max_attempts:
            password_attempts += 1
            print(f"\n--- 密码设置 (第 {password_attempts} 次尝试) ---")

            password = getpass.getpass("请输入密码 (至少6位): ").strip()

            # 检查密码长度
            if len(password) < 6:
                print("❌ 密码长度必须大于等于6位")
                if password_attempts < max_attempts:
                    print("请重新输入密码")
                    continue
                else:
                    print(f"已达到最大尝试次数 ({max_attempts})，脚本退出")
                    return

            # 密码强度检查（可选）
            if len(password) < 8:
                print("⚠️  建议密码长度至少8位以提高安全性")

            # 确认密码
            confirm_password = getpass.getpass("请确认密码: ").strip()

            if password == confirm_password:
                print("✅ 密码设置成功!")
                break
            else:
                print("❌ 两次输入的密码不一致")
                if password_attempts < max_attempts:
                    print("请重新输入密码")
                else:
                    print(f"已达到最大尝试次数 ({max_attempts})，脚本退出")
                    return

        # 获取手机号
        print("\n--- 手机号设置 ---")
        while True:
            mobile = input("请输入手机号 (11位数字): ").strip()

            # 检查手机号是否为空
            if not mobile:
                print("❌ 手机号不能为空，请重新输入")
                continue

            # 检查手机号格式
            if not mobile.isdigit():
                print("❌ 手机号只能包含数字，请重新输入")
                continue

            if len(mobile) != 11:
                print("❌ 手机号必须是11位数字，请重新输入")
                continue

            # 更严格的手机号格式验证
            if not re.match(r"^1[3-9]\d{9}$", mobile):
                print("❌ 手机号格式不正确，请输入有效的11位手机号")
                continue

            print(f"✅ 手机号设置为: {mobile}")
            break

        # 确认信息
        print("\n" + "=" * 40)
        print("📋 请确认用户信息:")
        print("=" * 40)
        print(f"用户名: {username}")
        print(f"手机号: {mobile}")
        print(f"权限: 超级管理员")
        print("=" * 40)

        while True:
            confirm = input("确认创建此用户吗? (y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                break
            elif confirm in ['n', 'no', '否']:
                print("❌ 用户取消创建操作")
                return
            else:
                print("请输入 y(是) 或 n(否)")

        # 创建超级用户
        print("\n--- 创建用户中 ---")
        try:
            user = await User.create(
                username=username,
                password=User.get_password_hash(password),
                mobile=mobile,
                status=1,
                is_superuser=True,
            )
            print(f"\n🎉 超级用户创建成功!")
            print(f"用户名: {username}")
            print(f"手机号: {mobile}")
            print(f"权限: 超级管理员")
            print(f"\n您现在可以使用这个账号登录系统了。")

        except Exception as create_error:
            print(f"❌ 创建用户时发生错误: {str(create_error)}")
            raise create_error

    except KeyboardInterrupt:
        print("\n\n⚠️  用户取消操作")
    except Exception as e:
        print(f"\n❌ 创建过程中发生错误: {str(e)}")
    finally:
        # 关闭数据库连接
        print("\n正在关闭数据库连接...")
        await Tortoise.close_connections()
        print("数据库连接已关闭")


def main():
    """主函数"""
    print("=" * 50)
    print("🚀 ZhuLinks 超级用户创建工具")
    print("=" * 50)
    print("此工具将帮助您创建系统超级管理员账号")
    print("超级管理员拥有系统的最高权限")
    print("-" * 50)

    try:
        asyncio.run(create_superuser())
    except KeyboardInterrupt:
        print("\n\n👋 再见!")
    except Exception as e:
        print(f"\n💥 程序异常退出: {str(e)}")
        sys.exit(1)

    print("\n" + "=" * 50)
    print("感谢使用 ZhuLinks 超级用户创建工具!")
    print("=" * 50)


if __name__ == "__main__":
    main()
