# -*- coding: utf-8 -*-
#!/usr/bin/env python3
"""
数据库表结构检查工具 - 交互式版本

这个工具可以帮助您检查数据库表结构并生成 Tortoise ORM 模型代码
支持多数据库连接，可以检查指定表或所有表
"""

import os
import shutil
import subprocess
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings


def create_temp_config(connection_name, table_name=None):
    """创建临时配置文件和 aerich 配置"""
    if connection_name not in settings.DATABASES:
        print(f"错误: 连接 '{connection_name}' 不存在于配置中")
        sys.exit(1)

    # 获取连接凭据
    connection_credentials = settings.DATABASES[connection_name]["credentials"]

    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="aerich_inspect_")

    # 创建临时配置文件
    config_content = f"""
# 临时配置，用于 aerich inspectdb
TORTOISE_ORM = {{
    "connections": {{
        "default": "postgres://{connection_credentials["user"]}:{connection_credentials["password"]}@{connection_credentials["host"]}:{connection_credentials["port"]}/{connection_credentials["database"]}"
    }},
    "apps": {{
        "models": {{
            "models": [],
            "default_connection": "default",
        }}
    }}
}}
"""

    # 写入配置文件
    config_path = Path(temp_dir) / "temp_config.py"
    with open(config_path, "w", encoding="utf-8") as f:
        f.write(config_content)

    # 创建 aerich 配置文件 (pyproject.toml)
    aerich_config = f"""
[tool.aerich]
tortoise_orm = "temp_config.TORTOISE_ORM"
location = "./migrations"
src_folder = "./"
"""

    pyproject_path = Path(temp_dir) / "pyproject.toml"
    with open(pyproject_path, "w", encoding="utf-8") as f:
        f.write(aerich_config)

    return temp_dir, config_path, pyproject_path


def run_inspectdb(temp_dir, table_name=None, output_path=None):
    """运行 aerich inspectdb 命令"""
    try:
        # 切换到临时目录
        original_cwd = os.getcwd()
        os.chdir(temp_dir)

        print(f"📁 工作目录: {temp_dir}")

        # 首先初始化 aerich
        print("🔧 初始化 aerich...")
        init_cmd = ["aerich", "init", "-t", "temp_config.TORTOISE_ORM"]
        init_result = subprocess.run(init_cmd, capture_output=True, text=True)

        if init_result.returncode != 0:
            print(f"❌ aerich 初始化失败:")
            print(f"错误信息: {init_result.stderr}")
            return False

        print("✅ aerich 初始化成功")

        # 构建 inspectdb 命令
        cmd = ["aerich", "inspectdb"]

        # 如果指定了表名，添加 --table 参数
        if table_name:
            cmd.extend(["--table", table_name])

        print(f"⚙️  执行命令: {' '.join(cmd)}")

        # 运行命令
        if output_path:
            # 确保输出目录存在
            output_file = Path(original_cwd) / output_path
            output_file.parent.mkdir(parents=True, exist_ok=True)

            print(f"📝 正在生成模型代码...")
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            if result.returncode == 0:
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(result.stdout)
                print(f"✅ 模型已保存到: {output_file}")
                print(f"📁 文件大小: {output_file.stat().st_size} bytes")
            else:
                print(f"❌ 命令执行失败:")
                print(f"错误信息: {result.stderr}")
                return False
        else:
            print(f"📋 生成的模型代码:")
            print("=" * 80)
            result = subprocess.run(cmd, text=True)
            if result.returncode != 0:
                print(f"❌ 命令执行失败")
                return False
            print("=" * 80)

        return True

    except FileNotFoundError:
        print("❌ 错误: 找不到 aerich 命令")
        print("请确保已安装 aerich: pip install aerich")
        return False
    except Exception as e:
        print(f"❌ 运行命令时出错: {e}")
        return False
    finally:
        # 恢复原始工作目录
        try:
            os.chdir(original_cwd)
        except:
            pass


def get_user_input():
    """获取用户输入的参数"""
    print("=" * 60)
    print("🔍 数据库表结构检查工具")
    print("=" * 60)
    print("这个工具可以帮助您检查数据库表结构并生成 Tortoise ORM 模型代码")
    print("-" * 60)

    # 1. 选择数据库连接
    print("\n📊 可用的数据库连接:")
    connections = list(settings.DATABASES.keys())
    for i, conn in enumerate(connections, 1):
        db_info = settings.DATABASES[conn]["credentials"]
        print(f"  {i}. {conn} - {db_info['host']}:{db_info['port']}/{db_info['database']}")

    while True:
        try:
            choice = input(f"\n请选择数据库连接 (1-{len(connections)}): ").strip()
            if choice.isdigit() and 1 <= int(choice) <= len(connections):
                connection = connections[int(choice) - 1]
                break
            else:
                print(f"❌ 请输入 1 到 {len(connections)} 之间的数字")
        except (ValueError, KeyboardInterrupt):
            print("\n👋 用户取消操作")
            sys.exit(0)

    print(f"✅ 已选择数据库连接: {connection}")

    # 2. 选择表名
    print(f"\n📋 表名设置:")
    print("  1. 检查所有表")
    print("  2. 检查指定表")

    while True:
        table_choice = input("\n请选择 (1-2): ").strip()
        if table_choice == "1":
            table = None
            print("✅ 将检查所有表")
            break
        elif table_choice == "2":
            table = input("请输入表名: ").strip()
            if table:
                print(f"✅ 将检查表: {table}")
                break
            else:
                print("❌ 表名不能为空")
        else:
            print("❌ 请输入 1 或 2")

    # 3. 选择输出方式
    print(f"\n💾 输出方式:")
    print("  1. 输出到控制台")
    print("  2. 保存到文件")

    while True:
        output_choice = input("\n请选择输出方式 (1-2): ").strip()
        if output_choice == "1":
            output_file = None
            print("✅ 将输出到控制台")
            break
        elif output_choice == "2":
            default_filename = f"{table or 'all_tables'}_{connection}_model.py"
            output_file = input(f"请输入文件路径 (默认: {default_filename}): ").strip()
            if not output_file:
                output_file = default_filename
            print(f"✅ 将保存到文件: {output_file}")
            break
        else:
            print("❌ 请输入 1 或 2")

    return {
        'connection': connection,
        'table': table,
        'output': output_file
    }


def main():
    """主函数"""
    try:
        # 获取用户输入
        params = get_user_input()

        print(f"\n🔄 开始检查数据库...")
        print(f"连接: {params['connection']}")
        print(f"表名: {params['table'] or '所有表'}")
        print(f"输出: {params['output'] or '控制台'}")

        # 创建临时配置
        temp_dir, config_path, pyproject_path = create_temp_config(params['connection'], params['table'])

        # 运行 inspectdb
        success = run_inspectdb(temp_dir, params['table'], params['output'])

        if not success:
            print("\n❌ 操作失败")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        sys.exit(1)
    finally:
        # 清理临时文件和目录
        if "temp_dir" in locals():
            try:
                shutil.rmtree(temp_dir)
                print(f"🧹 已清理临时目录: {temp_dir}")
            except Exception as e:
                print(f"⚠️  清理临时目录时出错: {e}")

    print(f"\n🎉 检查完成!")
    print("感谢使用数据库表结构检查工具!")


if __name__ == "__main__":
    main()
