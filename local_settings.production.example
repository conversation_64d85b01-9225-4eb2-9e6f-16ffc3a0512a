"""
生产环境配置示例
复制此文件为 local_settings.py 并根据实际环境修改配置
"""
from pathlib import Path

# 生产环境设置
DEBUG = False  # 生产环境必须设置为 False
API_V1_STR = "/api/v1"
PROJECT_NAME = "ZhuLinks API"

# 生产环境 CORS 设置 - 限制允许的域名
BACKEND_CORS_ORIGINS = [
    "https://yourdomain.com",
    "https://www.yourdomain.com",
    # 不要在生产环境使用 "*"
]

PROJECT_ROOT = Path(__file__).parent

# 生产环境日志设置
LOG_DIR = PROJECT_ROOT / "logs"

# 生产环境 Redis 设置
REDIS_HOST = "your-redis-host"
REDIS_PORT = "6379"
REDIS_DB = "0"
REDIS_PASSWORD = "your-redis-password"
REDIS_POOL_SIZE = 20  # 生产环境增加连接池大小
REDIS_POOL_TIMEOUT = 30

# 生产环境 JWT 设置
JWT_SECRET_KEY = "your-very-secure-jwt-secret-key-here-change-this-in-production"
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 生产环境数据库设置
DATABASES = {
    "default": {
        "engine": "tortoise.backends.asyncpg",
        "credentials": {
            "host": "your-db-host",
            "port": 5432,
            "user": "your-db-user",
            "password": "your-db-password",
            "database": "zhulinks_cert",
            "minsize": 5,   # 生产环境最小连接数
            "maxsize": 50,  # 生产环境最大连接数
            # 生产环境连接池优化
            "command_timeout": 60,
            "server_settings": {
                "jit": "off",  # 关闭 JIT 以提高稳定性
            },
        },
    },
    "orders": {
        "engine": "tortoise.backends.asyncpg",
        "credentials": {
            "host": "your-orders-db-host",
            "port": 5432,
            "user": "your-orders-db-user",
            "password": "your-orders-db-password",
            "database": "zhulinks_orders",
            "minsize": 2,
            "maxsize": 20,
            "command_timeout": 60,
            "server_settings": {
                "jit": "off",
            },
        },
    },
}

# 阿里云设置
ALIYUN_ACCESS_KEY_ID = "your-aliyun-access-key-id"
ALIYUN_ACCESS_KEY_SECRET = "your-aliyun-access-key-secret"

# 短信验证设置
SMS_CODE_LENGTH = 6
SMS_CODE_EXPIRE_MINUTES = 5

ALIYUN_SMS_SIGN_NAME = "your-sms-sign-name"
ALIYUN_SMS_TEMPLATE_CODE = "your-sms-template-code"

# 生产环境安全设置建议：
# 1. 使用强密码和复杂的密钥
# 2. 定期轮换密钥
# 3. 使用环境变量存储敏感信息
# 4. 启用数据库 SSL 连接
# 5. 配置防火墙规则
# 6. 使用 HTTPS
# 7. 定期备份数据库
# 8. 监控日志文件大小和磁盘空间
