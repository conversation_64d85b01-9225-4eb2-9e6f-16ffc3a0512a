#!/usr/bin/env python3
"""
测试日志配置脚本
用于验证生产环境日志配置是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入日志模块
from app.core import logging

def test_logging():
    """测试日志功能"""
    print("开始测试日志配置...")
    
    # 初始化日志系统
    logger = logging.setup()
    
    # 测试不同级别的日志
    logger.debug("这是一条DEBUG级别的日志")
    logger.info("这是一条INFO级别的日志")
    logger.warning("这是一条WARNING级别的日志")
    logger.error("这是一条ERROR级别的日志")
    
    # 测试异常日志
    try:
        1 / 0
    except Exception as e:
        logger.exception("测试异常日志记录")
    
    # 检查日志文件是否创建
    log_file = Path("logs/app.log")
    if log_file.exists():
        print(f"✅ 日志文件已创建: {log_file}")
        print(f"📁 日志文件大小: {log_file.stat().st_size} bytes")
    else:
        print("❌ 日志文件未创建")
    
    # 检查日志目录结构
    logs_dir = Path("logs")
    if logs_dir.exists():
        print(f"📂 日志目录内容:")
        for item in logs_dir.iterdir():
            print(f"   - {item.name}")
    
    print("日志配置测试完成!")

if __name__ == "__main__":
    test_logging()
