from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);
CREATE TABLE IF NOT EXISTS "cert_companies" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" VARCHAR(100) NOT NULL,
    "company_id" VARCHAR(100) NOT NULL UNIQUE,
    "province_id" VARCHAR(12),
    "city_id" VARCHAR(12),
    "district_id" VARCHAR(12),
    "address" VARCHAR(255),
    "contact_user" VARCHAR(32),
    "contact_phone" VARCHAR(20)
);
CREATE INDEX IF NOT EXISTS "idx_cert_compan_company_22b5d9" ON "cert_companies" ("company_id");
COMMENT ON COLUMN "cert_companies"."id" IS '自增主键';
COMMENT ON COLUMN "cert_companies"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_companies"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_companies"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_companies"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_companies"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_companies"."name" IS '公司名称';
COMMENT ON COLUMN "cert_companies"."company_id" IS '公司ID';
COMMENT ON COLUMN "cert_companies"."province_id" IS '省份ID';
COMMENT ON COLUMN "cert_companies"."city_id" IS '城市ID';
COMMENT ON COLUMN "cert_companies"."district_id" IS '区县ID';
COMMENT ON COLUMN "cert_companies"."address" IS '详细地址';
COMMENT ON COLUMN "cert_companies"."contact_user" IS '联系人';
COMMENT ON COLUMN "cert_companies"."contact_phone" IS '联系电话';
COMMENT ON TABLE "cert_companies" IS '公司信息表';
CREATE TABLE IF NOT EXISTS "cert_users" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "user_id" BIGINT NOT NULL UNIQUE,
    "username" VARCHAR(50) NOT NULL UNIQUE,
    "real_name" VARCHAR(12),
    "nickname" VARCHAR(64),
    "avatar" VARCHAR(255),
    "password" VARCHAR(255) NOT NULL,
    "mobile" VARCHAR(20) NOT NULL UNIQUE,
    "status" SMALLINT NOT NULL DEFAULT 1,
    "is_superuser" BOOL NOT NULL DEFAULT False,
    "last_login_time" TIMESTAMPTZ,
    "roles" JSONB,
    "company_id" BIGINT
);
COMMENT ON COLUMN "cert_users"."id" IS '自增主键';
COMMENT ON COLUMN "cert_users"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_users"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_users"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_users"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_users"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_users"."user_id" IS '用户ID';
COMMENT ON COLUMN "cert_users"."username" IS '用户名';
COMMENT ON COLUMN "cert_users"."real_name" IS '真实姓名';
COMMENT ON COLUMN "cert_users"."nickname" IS '昵称';
COMMENT ON COLUMN "cert_users"."avatar" IS '头像';
COMMENT ON COLUMN "cert_users"."password" IS '密码';
COMMENT ON COLUMN "cert_users"."mobile" IS '手机号';
COMMENT ON COLUMN "cert_users"."status" IS '激活状态(1:正常, 2:禁用)';
COMMENT ON COLUMN "cert_users"."is_superuser" IS '是否为超级管理员';
COMMENT ON COLUMN "cert_users"."last_login_time" IS '最后登录时间';
COMMENT ON COLUMN "cert_users"."roles" IS '角色列表';
COMMENT ON COLUMN "cert_users"."company_id" IS '公司ID';
COMMENT ON TABLE "cert_users" IS '用户信息表';
CREATE TABLE IF NOT EXISTS "cert_sequence" (
    "name" VARCHAR(100) NOT NULL PRIMARY KEY,
    "last" BIGINT NOT NULL DEFAULT 0
);
COMMENT ON COLUMN "cert_sequence"."name" IS '序号名称';
COMMENT ON COLUMN "cert_sequence"."last" IS '当前序号';
COMMENT ON TABLE "cert_sequence" IS '序号生成器';
CREATE TABLE IF NOT EXISTS "cert_shipping_subscribe" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "msg_from" VARCHAR(100),
    "logistics_company_id" VARCHAR(100),
    "logistics_company" VARCHAR(100),
    "logistics_order_id" VARCHAR(100) NOT NULL,
    "order_id" VARCHAR(100) NOT NULL,
    "send_date" TIMESTAMPTZ,
    "total_qty" INT NOT NULL DEFAULT 0,
    "items" JSONB,
    "create_date" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_logisti_b18ae1" ON "cert_shipping_subscribe" ("logistics_order_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_order_i_8a1021" ON "cert_shipping_subscribe" ("order_id");
COMMENT ON COLUMN "cert_shipping_subscribe"."msg_from" IS '消息来源:JST(聚水潭)/DD(抖店)';
COMMENT ON COLUMN "cert_shipping_subscribe"."logistics_company_id" IS '物流公司编码';
COMMENT ON COLUMN "cert_shipping_subscribe"."logistics_company" IS '物流公司';
COMMENT ON COLUMN "cert_shipping_subscribe"."logistics_order_id" IS '物流单号';
COMMENT ON COLUMN "cert_shipping_subscribe"."order_id" IS '线上单号';
COMMENT ON COLUMN "cert_shipping_subscribe"."send_date" IS '发货时间';
COMMENT ON COLUMN "cert_shipping_subscribe"."total_qty" IS '总数量';
COMMENT ON COLUMN "cert_shipping_subscribe"."items" IS '商品信息';
COMMENT ON COLUMN "cert_shipping_subscribe"."create_date" IS '创建时间';
COMMENT ON TABLE "cert_shipping_subscribe" IS '发货订阅表';
CREATE TABLE IF NOT EXISTS "cert_inbound_order" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "code" VARCHAR(32) NOT NULL,
    "check_company_name" VARCHAR(64) NOT NULL,
    "product_count" INT NOT NULL DEFAULT 1,
    "cert_count" INT NOT NULL DEFAULT 1,
    "waybill_count" INT NOT NULL DEFAULT 1,
    "status" INT NOT NULL DEFAULT 1,
    "check_user_id" BIGINT NOT NULL REFERENCES "cert_users" ("id") ON DELETE CASCADE,
    "company_id" BIGINT NOT NULL,
    CONSTRAINT "uid_cert_inboun_code_30fc48" UNIQUE ("code", "company_id")
);
CREATE INDEX IF NOT EXISTS "idx_cert_inboun_code_3f1ff0" ON "cert_inbound_order" ("code");
COMMENT ON COLUMN "cert_inbound_order"."id" IS '自增主键';
COMMENT ON COLUMN "cert_inbound_order"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_inbound_order"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_inbound_order"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_inbound_order"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_inbound_order"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_inbound_order"."code" IS '入库单号';
COMMENT ON COLUMN "cert_inbound_order"."check_company_name" IS '公司名称';
COMMENT ON COLUMN "cert_inbound_order"."product_count" IS '商品数量';
COMMENT ON COLUMN "cert_inbound_order"."cert_count" IS '证书数量';
COMMENT ON COLUMN "cert_inbound_order"."waybill_count" IS '面单数量';
COMMENT ON COLUMN "cert_inbound_order"."status" IS '状态(1:待扫描, 2:待发货, 3:发货中, 4:已完成)';
COMMENT ON COLUMN "cert_inbound_order"."check_user_id" IS '清点人员';
COMMENT ON COLUMN "cert_inbound_order"."company_id" IS '公司';
COMMENT ON TABLE "cert_inbound_order" IS '入库单';
CREATE TABLE IF NOT EXISTS "cert_inbound_order_detail" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "code" VARCHAR(32) NOT NULL,
    "shipping_type" INT NOT NULL DEFAULT 1,
    "cert_type" INT NOT NULL DEFAULT 1,
    "batch_count" INT NOT NULL DEFAULT 1,
    "product_count" INT NOT NULL DEFAULT 1,
    "cert_count" INT NOT NULL DEFAULT 1,
    "company_id" BIGINT NOT NULL,
    "inbound_order_id" BIGINT NOT NULL,
    "shipping_user_id" BIGINT,
    CONSTRAINT "uid_cert_inboun_code_a61472" UNIQUE ("code", "company_id")
);
CREATE INDEX IF NOT EXISTS "idx_cert_inboun_code_9bf3cb" ON "cert_inbound_order_detail" ("code");
COMMENT ON COLUMN "cert_inbound_order_detail"."id" IS '自增主键';
COMMENT ON COLUMN "cert_inbound_order_detail"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_inbound_order_detail"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_inbound_order_detail"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_inbound_order_detail"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_inbound_order_detail"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_inbound_order_detail"."code" IS '任务单号';
COMMENT ON COLUMN "cert_inbound_order_detail"."shipping_type" IS '发货类型(1:一单一件, 2:一单两件, 3:一单三件, 4:一单多件)';
COMMENT ON COLUMN "cert_inbound_order_detail"."cert_type" IS '发证方式(1:一物一证, 2:随机发证, 3:不发证书)';
COMMENT ON COLUMN "cert_inbound_order_detail"."batch_count" IS '每批数量';
COMMENT ON COLUMN "cert_inbound_order_detail"."product_count" IS '商品数量';
COMMENT ON COLUMN "cert_inbound_order_detail"."cert_count" IS '证书数量';
COMMENT ON COLUMN "cert_inbound_order_detail"."company_id" IS '公司';
COMMENT ON COLUMN "cert_inbound_order_detail"."inbound_order_id" IS '入库单';
COMMENT ON COLUMN "cert_inbound_order_detail"."shipping_user_id" IS '发货人';
COMMENT ON TABLE "cert_inbound_order_detail" IS '入库单明细';
CREATE TABLE IF NOT EXISTS "cert_shipping_waybill" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "waybill_code" VARCHAR(32) NOT NULL UNIQUE,
    "order_id" VARCHAR(128),
    "logistics_company" VARCHAR(100),
    "send_date" TIMESTAMPTZ,
    "total_qty" INT NOT NULL DEFAULT 0,
    "inbound_order_code" VARCHAR(32),
    "inbound_order_detail_code" VARCHAR(32),
    "shipping_user_name" VARCHAR(64),
    "company_id" BIGINT NOT NULL,
    "inbound_order_id" BIGINT NOT NULL,
    "inbound_order_detail_id" BIGINT NOT NULL,
    "shipping_user_id" BIGINT
);
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_waybill_8228df" ON "cert_shipping_waybill" ("waybill_code");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_order_i_20b68a" ON "cert_shipping_waybill" ("order_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_inbound_208a7e" ON "cert_shipping_waybill" ("inbound_order_code");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_inbound_a7e6cc" ON "cert_shipping_waybill" ("inbound_order_detail_code");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_waybill_3c45a5" ON "cert_shipping_waybill" ("waybill_code", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_order_i_a2d8bc" ON "cert_shipping_waybill" ("order_id", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_shippin_06bb1e" ON "cert_shipping_waybill" ("shipping_user_id", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_inbound_e193e1" ON "cert_shipping_waybill" ("inbound_order_code", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_inbound_00fcbe" ON "cert_shipping_waybill" ("inbound_order_detail_code", "company_id");
COMMENT ON COLUMN "cert_shipping_waybill"."id" IS '自增主键';
COMMENT ON COLUMN "cert_shipping_waybill"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_shipping_waybill"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_shipping_waybill"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_shipping_waybill"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_shipping_waybill"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_shipping_waybill"."waybill_code" IS '快递单号/面单编号';
COMMENT ON COLUMN "cert_shipping_waybill"."order_id" IS '线上订单号';
COMMENT ON COLUMN "cert_shipping_waybill"."logistics_company" IS '物流公司';
COMMENT ON COLUMN "cert_shipping_waybill"."send_date" IS '发货时间';
COMMENT ON COLUMN "cert_shipping_waybill"."total_qty" IS '总数量';
COMMENT ON COLUMN "cert_shipping_waybill"."inbound_order_code" IS '入库单号(冗余)';
COMMENT ON COLUMN "cert_shipping_waybill"."inbound_order_detail_code" IS '入库单明细号(冗余)';
COMMENT ON COLUMN "cert_shipping_waybill"."shipping_user_name" IS '发货人姓名(冗余)';
COMMENT ON COLUMN "cert_shipping_waybill"."company_id" IS '公司';
COMMENT ON COLUMN "cert_shipping_waybill"."inbound_order_id" IS '入库单';
COMMENT ON COLUMN "cert_shipping_waybill"."inbound_order_detail_id" IS '入库单明细';
COMMENT ON COLUMN "cert_shipping_waybill"."shipping_user_id" IS '发货人';
COMMENT ON TABLE "cert_shipping_waybill" IS '面单表';
CREATE TABLE IF NOT EXISTS "cert_waybill_product" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "item_type" INT NOT NULL DEFAULT 1,
    "sku_id" VARCHAR(128),
    "product_name" VARCHAR(256),
    "product_image" VARCHAR(500),
    "quantity" INT NOT NULL DEFAULT 0,
    "outer_oi_id" VARCHAR(128),
    "oi_id" VARCHAR(128),
    "cert_sn_code" VARCHAR(128) UNIQUE,
    "cert_type" INT,
    "product_sn_code" VARCHAR(128),
    "bind_status" INT NOT NULL DEFAULT 1,
    "bind_time" TIMESTAMPTZ,
    "waybill_code" VARCHAR(32),
    "order_id" VARCHAR(128),
    "company_id" BIGINT NOT NULL,
    "inbound_order_id" BIGINT NOT NULL,
    "inbound_order_detail_id" BIGINT NOT NULL,
    "waybill_id" BIGINT NOT NULL
);
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_item_ty_92699b" ON "cert_waybill_product" ("item_type");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_sku_id_2e6ca1" ON "cert_waybill_product" ("sku_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_outer_o_a8ab24" ON "cert_waybill_product" ("outer_oi_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_oi_id_64236e" ON "cert_waybill_product" ("oi_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_cert_sn_0a0e00" ON "cert_waybill_product" ("cert_sn_code");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_cert_ty_4daff1" ON "cert_waybill_product" ("cert_type");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_product_86b7a0" ON "cert_waybill_product" ("product_sn_code");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_bind_st_4e83bd" ON "cert_waybill_product" ("bind_status");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_waybill_34b692" ON "cert_waybill_product" ("waybill_code");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_order_i_8139f6" ON "cert_waybill_product" ("order_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_item_ty_d05867" ON "cert_waybill_product" ("item_type", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_sku_id_599cad" ON "cert_waybill_product" ("sku_id", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_cert_sn_dec909" ON "cert_waybill_product" ("cert_sn_code", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_product_8e1718" ON "cert_waybill_product" ("product_sn_code", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_outer_o_be9e9f" ON "cert_waybill_product" ("outer_oi_id", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_waybill_f0cb26" ON "cert_waybill_product" ("waybill_code", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_order_i_3e137a" ON "cert_waybill_product" ("order_id", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_cert_ty_ce2e82" ON "cert_waybill_product" ("cert_type", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_waybill_cfb411" ON "cert_waybill_product" ("waybill_id", "item_type");
COMMENT ON COLUMN "cert_waybill_product"."id" IS '自增主键';
COMMENT ON COLUMN "cert_waybill_product"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_waybill_product"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_waybill_product"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_waybill_product"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_waybill_product"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_waybill_product"."item_type" IS '记录类型(1:仅商品, 2:商品+证书, 3:随机发证)';
COMMENT ON COLUMN "cert_waybill_product"."sku_id" IS '商品SKU_ID';
COMMENT ON COLUMN "cert_waybill_product"."product_name" IS '商品名称';
COMMENT ON COLUMN "cert_waybill_product"."product_image" IS '商品图片';
COMMENT ON COLUMN "cert_waybill_product"."quantity" IS '商品数量';
COMMENT ON COLUMN "cert_waybill_product"."outer_oi_id" IS '外部订单项ID';
COMMENT ON COLUMN "cert_waybill_product"."oi_id" IS '订单项ID';
COMMENT ON COLUMN "cert_waybill_product"."cert_sn_code" IS '证书SN码';
COMMENT ON COLUMN "cert_waybill_product"."cert_type" IS '证书类型(1:一物一证, 2:随机发证)';
COMMENT ON COLUMN "cert_waybill_product"."product_sn_code" IS '商品SN码';
COMMENT ON COLUMN "cert_waybill_product"."bind_status" IS '绑定状态(1:未绑定, 2:已绑定)';
COMMENT ON COLUMN "cert_waybill_product"."bind_time" IS '绑定时间';
COMMENT ON COLUMN "cert_waybill_product"."waybill_code" IS '快递单号(冗余)';
COMMENT ON COLUMN "cert_waybill_product"."order_id" IS '线上订单号(冗余)';
COMMENT ON COLUMN "cert_waybill_product"."company_id" IS '公司';
COMMENT ON COLUMN "cert_waybill_product"."inbound_order_id" IS '入库单';
COMMENT ON COLUMN "cert_waybill_product"."inbound_order_detail_id" IS '入库单明细';
COMMENT ON COLUMN "cert_waybill_product"."waybill_id" IS '面单';
COMMENT ON TABLE "cert_waybill_product" IS '面单商品表（合并证书绑定）';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
